@extends('layouts.admin')
@section('page-title')
    {{__('Smart Scheduler')}}
@endsection
@section('action-btn')
<!-- Navigation Buttons -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body p-3">
                <div class="d-flex justify-content-center">
                    <div class="view-switcher-container w-100" style="overflow-x: auto;">
                        <div class="btn-group view-switcher flex-wrap d-flex justify-content-center gap-2" role="group" style="white-space: nowrap;">
                            <button type="button" onclick="switchView('calendar')" id="calendar-btn"
                                class="btn btn-sm bg-primary text-white view-btn d-flex align-items-center gap-1 px-3 py-2">
                                <i class="ti ti-calendar"></i>
                                <span class="btn-text">{{ __('Calendar') }}</span>
                            </button>

                            <button type="button" onclick="switchView('events')" id="events-btn"
                                class="btn btn-sm btn-outline-primary view-btn d-flex align-items-center gap-1 px-3 py-2">
                                <i class="ti ti-calendar-event"></i>
                                <span class="btn-text">{{ __('Create/Edit') }}</span>
                            </button>

                            <button type="button" onclick="switchView('bookings')" id="bookings-btn"
                                class="btn btn-sm btn-outline-primary view-btn d-flex flex-column flex-sm-row align-items-center justify-content-center gap-1 px-3 py-2 text-center w-100 w-sm-auto">
                                <i class="ti ti-users fs-5"></i>
                                <span class="btn-text">{{ __('Appointments') }}</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
@push('css-page')
    <link href='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css' rel='stylesheet' />
    <script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js'></script>
    <link href="{{ asset('css/calendar.css') }}" rel="stylesheet" />
    <link rel="stylesheet" href="{{ asset('css/summernote/summernote-bs4.css') }}">
    <style>
        /* Date Filter Styling */
        .date-filter-container {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 8px 12px;
            border: 1px solid #e9ecef;
        }

        .date-filter-container .form-control-sm {
            border-radius: 6px;
            border: 1px solid #ced4da;
            font-size: 0.875rem;
        }

        .date-filter-container .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            border-radius: 6px;
        }

        /* Status Dropdown Styling */
        .status-dropdown {
            position: relative;
            z-index: 9998;
        }
        
        .status-dropdown .dropdown-toggle {
            border: none;
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            border-radius: 6px;
            min-width: 100px;
            position: relative;
            z-index: 9998;
        }

        .status-dropdown .dropdown-toggle:focus {
            box-shadow: none;
        }

        .status-dropdown .dropdown-menu {
            border-radius: 8px;
            border: 1px solid #e9ecef;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            min-width: 150px;
            z-index: 9999 !important;
            position: fixed !important;
            margin-top: 2px !important;
        }

        .status-dropdown .dropdown-item {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            border-radius: 4px;
            margin: 2px 4px;
            transition: all 0.2s ease;
        }

        .status-dropdown .dropdown-item:hover {
            background-color: #f8f9fa;
            transform: translateX(2px);
        }

        .status-dropdown .dropdown-item i {
            width: 16px;
            text-align: center;
        }

        /* Ensure dropdown appears above modals and other elements */
        .status-dropdown.show .dropdown-menu {
            display: block !important;
            z-index: 99999 !important;
        }

        /* Fix for Bootstrap dropdown positioning */
        .dropdown-menu.show {
            z-index: 99999 !important;
        }

        /* Additional fixes for dropdown visibility */
        .table-responsive .dropdown-menu {
            z-index: 99999 !important;
        }

        /* Ensure dropdowns work in modal contexts */
        .modal .dropdown-menu {
            z-index: 999999 !important;
        }

        /* Fix for any container with overflow hidden */
        .dropdown {
            position: relative !important;
        }

        /* Ensure dropdown menu is visible above all other elements */
        .status-dropdown .dropdown-menu {
            transform: none !important;
            will-change: auto !important;
        }

        /* Style for dropdown menu when moved to body */
        body > .dropdown-menu {
            z-index: 999999 !important;
            position: fixed !important;
            background: white !important;
            border: 1px solid #e9ecef !important;
            border-radius: 8px !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        }

        .location-count-badge {
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
        }

        /* Pagination Styles */
        #events-pagination {
            border-top: 1px solid #dee2e6;
            background-color: #f8f9fa;
        }

        .pagination-info {
            color: #6c757d;
            font-size: 0.875rem;
        }

        .pagination .page-link {
            color: #6c757d;
            border: 1px solid #dee2e6;
            padding: 0.375rem 0.75rem;
            margin: 0 0.125rem;
            border-radius: 0.375rem;
            transition: all 0.15s ease-in-out;
        }

        .pagination .page-link:hover {
            color: #0d6efd;
            background-color: #e9ecef;
            border-color: #dee2e6;
        }

        .pagination .page-item.active .page-link {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
        }

        .pagination .page-item.disabled .page-link {
            color: #adb5bd;
            background-color: #fff;
            border-color: #dee2e6;
        }

        #events-per-page {
            min-width: 70px;
        }

        /* Bulk Actions Styles */
        #bulk-actions {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: #f8f9fa;
        }

        #selected-count {
            font-size: 0.875rem;
            font-weight: 500;
        }

        #bulk-status {
            min-width: 120px;
        }

        .form-check-input:indeterminate {
            background-color: #0d6efd;
            border-color: #0d6efd;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
        }
        
        /* Date Range Selection Styling */
        .form-check.date-range-option {
            transition: all 0.2s ease;
            padding: 0.75rem;
            border-radius: 0.5rem;
            border: 1px solid #e9ecef;
        }
        
        .form-check.date-range-option:hover {
            background-color: #f8f9fa;
            border-color: #22c55e;
        }
        
        .form-check.date-range-option .form-check-input:checked + .form-check-label {
            color: #22c55e;
            font-weight: 600;
        }
        
        .form-check.date-range-option .form-check-input:checked {
            background-color: #22c55e;
            border-color: #22c55e;
        }

        .event-checkbox {
            cursor: pointer;
        }

        #select-all-events {
            cursor: pointer;
        }

        /* Appointment Filter Styling */
        .appointment-filter-group .btn {
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .appointment-filter-group .btn:first-child {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .appointment-filter-group .btn:not(:first-child):not(:last-child) {
            border-radius: 0;
            border-left: none;
        }

        .appointment-filter-group .btn:last-child {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-left: none;
        }

        .appointment-filter-group .btn.active {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
        }

        .appointment-filter-group .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* Calendar Filter Section Styling */
        .calendar-filter-section {
            background: transparent !important;
            border-radius: 0 !important;
            padding: 0 20px !important;
            border: none !important;
            margin-bottom: 15px;
        }

        /* Override modern calendar card background */
        .modern-calendar-card {
            background: white !important;
        }

        .modern-calendar-card::before {
            display: none !important;
        }

        .calendar-filter-section .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }

        .calendar-filter-section .form-select {
            border-radius: 6px;
            border: 1px solid #ced4da;
            font-size: 0.875rem;
        }

        .calendar-filter-section .btn-outline-secondary {
            border-radius: 6px;
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
        }

        .calendar-filter-section .btn-outline-secondary:hover {
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
        }

        /* Header Status Legend Styling */
        .header-status-legend {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.8rem;
            font-weight: 500;
            color: #495057;
            padding: 4px 8px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            border: 1px solid #fff;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .booked-dot {
            background-color: #007bff;
        }

        .cancelled-dot {
            background-color: #dc3545;
        }

        .show-up-dot {
            background-color: #28a745;
        }

        .no-show-dot {
            background-color: #ffc107;
        }

        /* Responsive design for header legend */
        @media (max-width: 768px) {
            .header-status-legend {
                gap: 8px;
                justify-content: center;
                margin-top: 10px;
            }
            
            .status-item {
                font-size: 0.75rem;
                padding: 3px 6px;
            }
            
            .status-dot {
                width: 8px;
                height: 8px;
            }
        }

        /* Contact Dropdown Styling */
        #appointment_contact_name {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            background-color: #fff;
        }

        #appointment_contact_name:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        #appointment_contact_name option {
            padding: 8px 12px;
            border-bottom: 1px solid #f8f9fa;
        }

        #appointment_contact_name option:hover {
            background-color: #f8f9fa;
        }

        /* Contact selection feedback */
        .contact-selected {
            border-color: #198754 !important;
            background-color: #f8fff9 !important;
        }

        /* Loading spinner animation */
        .ti-spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Readonly placeholder input styling */
        .readonly-placeholder {
            background-color: #f8f9fa !important;
            color: #6c757d !important;
            cursor: not-allowed !important;
        }

        .readonly-placeholder:focus {
            background-color: #f8f9fa !important;
            border-color: #ced4da !important;
            box-shadow: none !important;
        }

        /* Mobile Responsive Table Styles */
        @media (max-width: 768px) {
            /* Events Table Mobile Responsiveness */
            .table-responsive {
                border: 1px solid #dee2e6;
                border-radius: 0.375rem;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                scrollbar-width: thin;
                scrollbar-color: #6c757d #f8f9fa;
                position: relative;
            }

            /* Add scroll indicator for mobile users */
            .table-responsive::after {
                content: '← Scroll →';
                position: absolute;
                bottom: 0;
                right: 0;
                background: rgba(13, 110, 253, 0.9);
                color: white;
                padding: 0.25rem 0.5rem;
                font-size: 0.7rem;
                border-radius: 0.25rem 0 0.375rem 0;
                pointer-events: none;
                z-index: 10;
                animation: fadeInOut 3s ease-in-out infinite;
            }

            @keyframes fadeInOut {
                0%, 100% { opacity: 0.7; }
                50% { opacity: 1; }
            }

            /* Hide scroll indicator when table is fully scrolled */
            .table-responsive.scrolled::after {
                display: none;
            }

            .table-responsive::-webkit-scrollbar {
                height: 8px;
            }

            .table-responsive::-webkit-scrollbar-track {
                background: #f8f9fa;
                border-radius: 4px;
            }

            .table-responsive::-webkit-scrollbar-thumb {
                background: #6c757d;
                border-radius: 4px;
            }

            .table-responsive::-webkit-scrollbar-thumb:hover {
                background: #495057;
            }

            /* Ensure tables maintain minimum width for proper column display */
            #events-table {
                min-width: 800px;
                white-space: nowrap;
            }

            #bookings-table {
                min-width: 1200px;
                white-space: nowrap;
            }

            /* Optimize table cell content for mobile */
            .table td, .table th {
                padding: 0.5rem 0.25rem;
                font-size: 0.875rem;
                vertical-align: middle;
            }

            /* Make action buttons smaller on mobile */
            .table .btn-sm {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
            }

            /* Improve dropdown positioning in mobile tables */
            .table .dropdown-menu {
                position: fixed !important;
                z-index: 99999 !important;
                transform: none !important;
            }

            /* Card header adjustments for mobile */
            .card-header {
                padding: 0.75rem;
            }

            .card-header h5 {
                font-size: 1rem;
                margin-bottom: 0.5rem;
            }

            /* Filter controls mobile optimization */
            .date-filter-container {
                flex-direction: column;
                gap: 0.5rem;
                padding: 0.5rem;
            }

            .date-filter-container .form-control-sm {
                width: 100% !important;
            }

            .appointment-filter-group {
                flex-direction: column;
                width: 100%;
            }

            .appointment-filter-group .btn {
                border-radius: 0.375rem !important;
                margin-bottom: 0.25rem;
            }

            /* Bulk actions mobile optimization */
            #bulk-actions, #bulk-booking-actions {
                flex-direction: column;
                gap: 0.5rem;
                padding: 0.5rem;
                width: 100%;
            }

            /* Pagination mobile optimization */
            .pagination {
                flex-wrap: wrap;
                justify-content: center;
            }

            .pagination .page-link {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
            }

            /* Status badges mobile optimization */
            .badge {
                font-size: 0.7rem;
                padding: 0.25rem 0.5rem;
            }

            /* Location badges mobile optimization */
            .location-count-badge {
                font-size: 0.6rem;
                padding: 0.15rem 0.3rem;
            }
        }

        /* Tablet responsive adjustments */
        @media (min-width: 769px) and (max-width: 1024px) {
            .table-responsive {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            #events-table {
                min-width: 700px;
            }

            #bookings-table {
                min-width: 1000px;
            }

            .table td, .table th {
                padding: 0.6rem 0.4rem;
                font-size: 0.9rem;
            }
        }
    </style>
@endpush

@php
    $setting = \App\Models\Utility::settings();
@endphp

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('Smart Scheduler')}}</li>
@endsection

@section('content')
<!-- Create Event Modal -->
<div class="modal fade" id="createEventModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Create New Event') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createEventForm">
                <div class="modal-body">
                    @csrf
                    <!-- PART 1: Event Details -->
                    <div class="form-part">
                        <div class="part-title">{{ __('Event Details') }}</div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="event_title" class="form-label">{{ __(' Title') }} *</label>
                                <input type="text" class="form-control" id="event_title" name="title" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="assigned_staff_id" class="form-label">{{ __('Assign a Staff') }}</label>
                                <select class="form-control" id="assigned_staff_id" name="assigned_staff_id">
                                    <option value="">{{ __('Select Staff (Optional)') }}</option>
                                </select>
                            </div>
                        </div>

                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="event_duration" class="form-label">{{ __('Duration (Minutes)') }}</label>
                                <input type="number" class="form-control" id="event_duration" name="duration" min="1" max="1440" value="60">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="booking_per_slot" class="form-label">{{ __('Booking Per Slot') }}</label>
                                <input type="number" class="form-control" id="booking_per_slot" name="booking_per_slot" min="1" max="100" value="1">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="minimum_notice_value" class="form-label text-primary fw-semibold">{{ __('Minimum Scheduling Notice') }}</label>
                                <div class="d-flex gap-2 align-items-stretch">
                                    <div class="flex-fill">
                                        <input type="number" class="form-control border-primary" id="minimum_notice_value" name="minimum_notice_value" min="0" max="999" value="0" placeholder="0" style="border-width: 2px;">
                                    </div>
                                    <div class="flex-fill">
                                        <select class="form-control border-primary" id="minimum_notice_unit" name="minimum_notice_unit" style="border-width: 2px;">
                                            <option value="minutes">{{ __('Minutes') }}</option>
                                            <option value="hours">{{ __('Hours') }}</option>
                                            <option value="days">{{ __('Days') }}</option>
                                            <option value="months">{{ __('Months') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <small class="text-muted mt-1" id="minimum_notice_preview">No minimum notice required</small>
                                <!-- Hidden field to store the calculated minutes for backend compatibility -->
                                <input type="hidden" id="minimum_notice" name="minimum_notice" value="0">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="event_description" class="form-label">{{ __('Description') }}</label>
                            <textarea class="form-control summernote-simple" id="event_description" name="description" rows="3"></textarea>
                        </div>

                        <!-- Payment Settings Section -->
                        <div class="mb-4">
                            <div class="part-title mb-3">{{ __('Payment Settings') }}</div>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="payment_required" name="payment_required" value="1">
                                        <label class="form-check-label" for="payment_required">
                                            <i class="ti ti-credit-card me-2"></i>{{ __('Payment Required') }}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="payment_amount" class="form-label">{{ __('Amount') }}</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="payment_amount" name="payment_amount" min="0" step="0.01" placeholder="0.00" disabled>
                                        <select class="form-control" id="payment_currency" name="payment_currency" style="max-width: 100px;" disabled>
                                            <option value="USD">USD</option>
                                            <option value="EUR">EUR</option>
                                            <option value="GBP">GBP</option>
                                            <option value="INR">INR</option>
                                            <option value="CAD">CAD</option>
                                            <option value="AUD">AUD</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="form-text">
                                        <i class="ti ti-info-circle me-1"></i>
                                        {{ __('Enable payment to charge customers for booking this event.') }}
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Partial Payment Settings -->
                            <div class="row mt-3" id="partial_payment_settings" style="display: none;">
                                <div class="col-md-4 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="partial_payment_enabled" name="partial_payment_enabled" value="1">
                                        <label class="form-check-label" for="partial_payment_enabled">
                                            <i class="ti ti-percentage me-2"></i>{{ __('Allow Partial Payment') }}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="partial_payment_percentage" class="form-label">{{ __('Partial Payment %') }}</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="partial_payment_percentage" name="partial_payment_percentage" min="10" max="90" step="5" placeholder="50" disabled>
                                        <span class="input-group-text">%</span>
                                    </div>
                                    <small class="text-muted">{{ __('Minimum 10%, Maximum 90% of total amount') }}</small>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="form-text">
                                        <i class="ti ti-info-circle me-1"></i>
                                        {{ __('Allow customers to pay a percentage of the total amount upfront.') }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Date Range Section -->
                        <div class="mb-4">
                            <div class="part-title mb-2">{{ __('Date Range') }}</div>
                            <p class="text-muted mb-3">{{ __('Invitees can schedule...') }}</p>

                            <!-- Calendar Days Option -->
                            <div class="form-check mb-3 date-range-option">
                                <input class="form-check-input" type="radio" name="date_range_type" id="calendar_days_option" value="calendar_days">
                                <label class="form-check-label w-100" for="calendar_days_option">
                                    <div class="row g-2 align-items-center">
                                        <div class="col-auto">
                                            <input type="number" class="form-control form-control-sm" id="calendar_days_input"
                                                name="date_range_days" style="width: 90px;" min="1" max="365" value="30" disabled>
                                        </div>
                                        <div class="col-auto">
                                            <span>{{ __('calendar days into the future') }}</span>
                                        </div>
                                    </div>
                                </label>
                            </div>

                            <!-- Date Range Option -->
                            <div class="form-check mb-3 date-range-option">
                                <input class="form-check-input" type="radio" name="date_range_type" id="date_range_option" value="date_range">
                                <label class="form-check-label w-100" for="date_range_option">
                                    <div class="row gx-2 gy-2 align-items-center">
                                        <div class="col-12 col-sm-auto">
                                            <span>{{ __('Within a date range') }}</span>
                                        </div>
                                        <div class="col-12 col-sm-auto">
                                            <input type="date" class="form-control form-control-sm" id="date_range_start_input"
                                                name="date_range_start" style="min-width: 150px;" disabled>
                                        </div>
                                        <div class="col-auto text-muted d-none d-sm-inline">–</div>
                                        <div class="col-12 col-sm-auto">
                                            <input type="date" class="form-control form-control-sm" id="date_range_end_input"
                                                name="date_range_end" style="min-width: 150px;" disabled>
                                        </div>
                                    </div>
                                </label>
                            </div>

                            <!-- Indefinitely Option -->
                            <div class="form-check mb-3 date-range-option">
                                <input class="form-check-input" type="radio" name="date_range_type" id="indefinitely_option" value="indefinitely" checked>
                                <label class="form-check-label" for="indefinitely_option">
                                    {{ __('Indefinitely into the future') }}
                                </label>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="event_location" class="form-label">{{ __('Location') }} <span class="text-danger">*</span></label>

                                <!-- Selected Locations Display -->
                                <div id="selected-locations-container" class="mb-2">
                                    <!-- Selected location chips will appear here -->
                                </div>

                                <!-- Location Dropdown -->
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary dropdown-toggle w-100 text-start" type="button" id="locationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="ti ti-map-pin me-2"></i>{{ __('Add a location') }}
                                    </button>
                                    <ul class="dropdown-menu w-100" aria-labelledby="locationDropdown">
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="openLocationModal('zoom')">
                                                <i class="ti ti-video me-2 text-primary"></i>
                                                <div>
                                                    <strong>{{ __('Zoom') }}</strong>
                                                    <br><small class="text-muted">{{ __('Online meeting/conference') }}</small>
                                                </div>
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="openLocationModal('in_person')">
                                                <i class="ti ti-map-pin me-2 text-success"></i>
                                                <div>
                                                    <strong>{{ __('In-person meeting') }}</strong>
                                                    <br><small class="text-muted">{{ __('Set an address or place') }}</small>
                                                </div>
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="openLocationModal('phone')">
                                                <i class="ti ti-phone me-2 text-info"></i>
                                                <div>
                                                    <strong>{{ __('Phone call') }}</strong>
                                                    <br><small class="text-muted">{{ __('Incoming or Outgoing calls') }}</small>
                                                </div>
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="openLocationModal('meet')">
                                                <i class="ti ti-brand-google me-2 text-warning"></i>
                                                <div>
                                                    <strong>{{ __('Google Meet') }}</strong>
                                                    <br><small class="text-muted">{{ __('Online meeting/conference') }}</small>
                                                </div>
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="openLocationModal('skype')">
                                                <i class="ti ti-brand-skype me-2 text-primary"></i>
                                                <div>
                                                    <strong>{{ __('Skype') }}</strong>
                                                    <br><small class="text-muted">{{ __('Online meeting/conference') }}</small>
                                                </div>
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="openLocationModal('others')">
                                                <i class="ti ti-dots me-2 text-secondary"></i>
                                                <div>
                                                    <strong>{{ __('Others') }}</strong>
                                                    <br><small class="text-muted">{{ __('Custom location type') }}</small>
                                                </div>
                                            </a>
                                        </li>
                                    </ul>
                                </div>

                                <!-- Hidden input to store location data -->
                                <input type="hidden" id="event_locations_data" name="locations_data" value="">
                            </div>
                        </div>

                        <!-- Custom Redirect URL Section -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="custom_redirect_url" class="form-label">
                                    <i class="ti ti-external-link me-2"></i>{{ __('Custom Redirect URL') }}
                                    <small class="text-muted">({{ __('Optional') }})</small>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="ti ti-link"></i>
                                    </span>
                                    <input type="url"
                                           class="form-control"
                                           id="custom_redirect_url"
                                           name="custom_redirect_url"
                                           placeholder="{{ __('https://example.com/thank-you') }}"
                                           pattern="https?://.*">
                                </div>
                                <div class="form-text">
                                    <i class="ti ti-info-circle me-1"></i>
                                    {{ __('If provided, users will be redirected to this URL after booking confirmation instead of the default confirmation page.') }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-part">
    <div class="part-title">{{ __('Custom Fields') }}</div>
    <div class="row">
        <div class="col-md-6 mb-3">
            <label for="custom_field" class="form-label">{{ __('Field Type') }}</label>
            <select class="form-control" id="custom_field" name="custom_field[]" onchange="toggleCustomField()">
                <option value="">{{ __('No Custom Field') }}</option>
            </select>
        </div>
        <div class="col-md-6 mb-3 d-flex align-items-end">
            <button type="button" class="btn btn-primary" onclick="addCustomField()">
                <i class="ti ti-plus me-1"></i>{{ __('Add Field') }}
            </button>
        </div>
    </div>

    <!-- Dynamic Custom Fields Container -->
    <div id="custom-fields-container">
        <!-- Multiple custom fields will be added here dynamically -->
    </div>
</div>

<div class="form-part">
    <div class="part-title">{{ __('Weekly Availability') }}</div>
    <div class="week-availability-vertical">
        <!-- Monday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="monday-checkbox" name="availability[monday][enabled]" checked>
                <label for="monday-checkbox">{{ __('Monday') }}</label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="monday" onclick="addTimeSlot('monday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="monday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[monday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[monday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('monday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Tuesday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="tuesday-checkbox" name="availability[tuesday][enabled]" checked>
                <label for="tuesday-checkbox">{{ __('Tuesday') }}</label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="tuesday" onclick="addTimeSlot('tuesday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="tuesday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[tuesday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[tuesday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('tuesday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Wednesday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="wednesday-checkbox" name="availability[wednesday][enabled]" checked>
                <label for="wednesday-checkbox">{{ __('Wednesday') }}</label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="wednesday" onclick="addTimeSlot('wednesday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="wednesday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[wednesday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[wednesday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('wednesday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Thursday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="thursday-checkbox" name="availability[thursday][enabled]" checked>
                <label for="thursday-checkbox">{{ __('Thursday') }}</label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="thursday" onclick="addTimeSlot('thursday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="thursday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[thursday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[thursday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('thursday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Friday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="friday-checkbox" name="availability[friday][enabled]" checked>
                <label for="friday-checkbox">{{ __('Friday') }}</label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="friday" onclick="addTimeSlot('friday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="friday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[friday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[friday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('friday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Saturday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="saturday-checkbox" name="availability[saturday][enabled]" checked>
                <label for="saturday-checkbox">{{ __('Saturday') }}</label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="saturday" onclick="addTimeSlot('saturday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="saturday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[saturday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[saturday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('saturday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Sunday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="sunday-checkbox" name="availability[sunday][enabled]" checked>
                <label for="sunday-checkbox">{{ __('Sunday') }}</label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="sunday" onclick="addTimeSlot('sunday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="sunday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[sunday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[sunday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('sunday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="mb-4">
    <div class="row">
        <div class="col-md-8">
            <label for="override_date" class="form-label" style="margin-left: 10px;">{{ __('Add date overrides') }}</label>
            <div class="row g-2 mb-3">
                <div class="col-md-4">
                    <input type="date" class="form-control" id="override_date" placeholder="Select date" style="border-radius: 8px;">
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="override_time" style="border-radius: 8px;">
                        <option value="">Select time</option>
                        <option value="00:00">12:00 AM</option>
                        <option value="00:30">12:30 AM</option>
                        <option value="01:00">01:00 AM</option>
                        <option value="01:30">01:30 AM</option>
                        <option value="02:00">02:00 AM</option>
                        <option value="02:30">02:30 AM</option>
                        <option value="03:00">03:00 AM</option>
                        <option value="03:30">03:30 AM</option>
                        <option value="04:00">04:00 AM</option>
                        <option value="04:30">04:30 AM</option>
                        <option value="05:00">05:00 AM</option>
                        <option value="05:30">05:30 AM</option>
                        <option value="06:00">06:00 AM</option>
                        <option value="06:30">06:30 AM</option>
                        <option value="07:00">07:00 AM</option>
                        <option value="07:30">07:30 AM</option>
                        <option value="08:00">08:00 AM</option>
                        <option value="08:30">08:30 AM</option>
                        <option value="09:00">09:00 AM</option>
                        <option value="09:30">09:30 AM</option>
                        <option value="10:00">10:00 AM</option>
                        <option value="10:30">10:30 AM</option>
                        <option value="11:00">11:00 AM</option>
                        <option value="11:30">11:30 AM</option>
                        <option value="12:00">12:00 PM</option>
                        <option value="12:30">12:30 PM</option>
                        <option value="13:00">01:00 PM</option>
                        <option value="13:30">01:30 PM</option>
                        <option value="14:00">02:00 PM</option>
                        <option value="14:30">02:30 PM</option>
                        <option value="15:00">03:00 PM</option>
                        <option value="15:30">03:30 PM</option>
                        <option value="16:00">04:00 PM</option>
                        <option value="16:30">04:30 PM</option>
                        <option value="17:00">05:00 PM</option>
                        <option value="17:30">05:30 PM</option>
                        <option value="18:00">06:00 PM</option>
                        <option value="18:30">06:30 PM</option>
                        <option value="19:00">07:00 PM</option>
                        <option value="19:30">07:30 PM</option>
                        <option value="20:00">08:00 PM</option>
                        <option value="20:30">08:30 PM</option>
                        <option value="21:00">09:00 PM</option>
                        <option value="21:30">09:30 PM</option>
                        <option value="22:00">10:00 PM</option>
                        <option value="22:30">10:30 PM</option>
                        <option value="23:00">11:00 PM</option>
                        <option value="23:30">11:30 PM</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="button" class="btn btn-primary w-100" onclick="addUnavailableSlot()" style="border-radius: 8px;">
                        <i class="ti ti-plus me-1"></i>{{ __('Add') }}
                    </button>
                </div>
            </div>
            <div id="unavailable-slots-list" class="mt-3">
                <!-- Dynamically added slots go here -->
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="form-part">
                <label for="event_status" class="form-label">{{ __('Event Status') }} *</label>
                <select class="form-control" id="event_status" name="status" required>
                    <option value="active">{{ __('Active') }}</option>
                    <option value="inactive">{{ __('Inactive') }}</option>
                </select>
                <small class="text-muted">{{ __('Active events can be booked by users. Inactive events are hidden from booking.') }}</small>
            </div>
        </div>
    </div>
</div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-primary" id="submitBtn">{{ __('Create Event') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>
</div>
<!-- View Event Modal -->
<div class="modal fade" id="viewEventModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="ti ti-calendar-event me-2"></i>{{ __('Event Details') }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-4">
                <!-- Event Title -->
                <div class="text-center mb-4 pb-3 border-bottom">
                    <h3 class="text-primary mb-1" id="view-event-title"></h3>
                    <small class="text-muted">Event Information</small>
                </div>
                
                <!-- Event Details Grid -->
                <div class="row g-4">
                    <!-- Event Settings Section -->
                    <div class="col-12">
                        <h6 class="text-secondary mb-3">
                            <i class="ti ti-settings me-2"></i>Event Settings
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <i class="ti ti-clock text-info fs-3 mb-2"></i>
                                    <div class="fw-bold text-dark">Duration</div>
                                    <div class="text-muted" id="view-event-duration"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <i class="ti ti-users text-warning fs-3 mb-2"></i>
                                    <div class="fw-bold text-dark">Booking Slots</div>
                                    <div class="text-muted" id="view-event-booking-slots"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <i class="ti ti-bell text-secondary fs-3 mb-2"></i>
                                    <div class="fw-bold text-dark">Notice Required</div>
                                    <div class="text-muted" id="view-event-notice"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Description Section -->
                    <div class="col-12" id="view-description-row" style="display: none;">
                        <h6 class="text-secondary mb-3">
                            <i class="ti ti-file-text me-2"></i>Description
                        </h6>
                        <div class="p-3 bg-light rounded">
                            <div id="view-event-description" class="text-dark"></div>
                        </div>
                    </div>
                    
                    <!-- Location Section -->
                    <div class="col-12" id="view-location-section" style="display: none;">
                        <div class="d-flex align-items-center mb-4">
                            <div class="icon-wrapper-modern me-3">
                                <i class="ti ti-map-pin text-primary"></i>
                            </div>
                            <div>
                                <h5 class="fw-bold text-dark mb-0">Location Details</h5>
                                <small class="text-muted">Where this event will take place</small>
                            </div>
                        </div>

                        <!-- Locations Container with Dynamic Column Layout -->
                        <div class="row g-3" id="view-locations-container">
                            <!-- Physical Address -->
                            <div id="view-address-row" style="display: none;">
                                <div class="p-4 border rounded bg-white shadow-sm h-100">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="flex-shrink-0">
                                            <div class="icon-wrapper-modern">
                                                <i class="ti ti-map-pin text-success fs-5"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="fw-bold text-dark mb-0">Physical Address</h6>
                                            <small class="text-muted">Meeting location</small>
                                        </div>
                                    </div>
                                    <div class="address-content">
                                        <p class="mb-0 text-dark" id="view-event-address"></p>
                                    </div>
                                </div>
                            </div>

                            <!-- Meeting Locations -->
                            <div id="view-link-row" style="display: none;">
                                <div class="p-4 border rounded bg-white shadow-sm h-100">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="flex-shrink-0">
                                            <div class="icon-wrapper-modern">
                                                <i class="ti ti-video text-primary fs-5"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="fw-bold text-dark mb-0">Meeting Locations</h6>
                                            <small class="text-muted">Click to join</small>
                                        </div>
                                    </div>
                                    <div id="view-meeting-locations-container" class="modern-locations-list">
                                        <!-- Multiple meeting locations will be displayed here -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Legacy Location Type Display (for backward compatibility) -->
                        <div class="mb-3" id="view-location-row" style="display: none;">
                            <div class="d-flex align-items-center p-3 border rounded">
                                <div class="flex-shrink-0">
                                    <i class="ti ti-map-pin text-success fs-4"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="fw-bold text-dark">Location Type</div>
                                    <div class="text-muted" id="view-event-location"></div>
                                </div>
                            </div>
                        </div>
                </div>
                </div>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="ti ti-x me-1"></i>{{ __('Close') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modern Calendar Section -->
<div class="row" id="calendar-section">
    <div class="col-xl-9 col-lg-8 col-md-12 mb-4">
        <div class="modern-calendar-card">
            <div class="modern-calendar-header">
                <div class="calendar-title-section">
                    <div class="calendar-icon-wrapper bg-primary">
                        <i class="fas fa-address-card"></i>
                    </div>
                    <div>
                        <!-- <h4 class="calendar-title">{{ __('Calendar') }}</h4> -->
                        <h5>{{ __('Manage your Appointments') }}</h5>
                    </div>
                </div>
                <!-- Status Legend in Header -->
                <div class="header-status-legend">
                    <div class="status-item">
                        <div class="status-dot booked-dot"></div>
                        <span>{{ __('Booked') }}</span>
                    </div>
                    <div class="status-item">
                        <div class="status-dot cancelled-dot"></div>
                        <span>{{ __('Cancelled') }}</span>
                    </div>
                    <div class="status-item">
                        <div class="status-dot show-up-dot"></div>
                        <span>{{ __('Show Up') }}</span>
                    </div>
                    <div class="status-item">
                        <div class="status-dot no-show-dot"></div>
                        <span>{{ __('No Show') }}</span>
                    </div>
                </div>
            </div>
            
            <!-- Filter Section -->
            <div class="calendar-filter-section mb-3">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <label for="event-filter" class="form-label mb-1">
                            <i class="ti ti-filter me-1"></i>{{ __('Filter by Event') }}
                        </label>
                        <select id="event-filter" class="form-select">
                            <option value="">{{ __('All Events') }}</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="staff-filter" class="form-label mb-1">
                            <i class="ti ti-user me-1"></i>{{ __('Filter by Staff') }}
                        </label>
                        <select id="staff-filter" class="form-select">
                            <option value="">{{ __('All Staff') }}</option>
                        </select>
                    </div>
                </div>
            </div>
            

            
            <div class="modern-calendar-body">
                <div id='calendar' class="modern-fullcalendar"></div>
            </div>
        </div>
    </div>

    <!-- Modern Sidebar -->
    <div class="col-xl-3 col-lg-4 col-md-12 mb-4">
        <div class="modern-sidebar-card">
            <div class="sidebar-header">
                <div class="date-display">
                    <div class="current-date">
                        <h5 class="date-title" id="selected-date-title">{{ __('Today') }}</h5>
                        <p class="date-subtitle" id="selected-date-subtitle">{{ date('F j, Y') }}</p>
                    </div>
                    <div class="date-icon">
                        <div class="icon-circle">
                            <i class="ti ti-calendar-time"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="sidebar-body">
                <div id="selected-date-events">
                    <div class="empty-state" id="no-events-message">
                        <div class="empty-icon">
                            <i class="ti ti-calendar-off"></i>
                        </div>
                        <h6 class="empty-title">{{ __('No Events') }}</h6>
                        <p class="empty-text">{{ __('Click on a date to view events') }}</p>
                    </div>
                    <div class="events-container" id="events-list" style="display: none;">
                        <!-- Events will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Events Section -->
<div class="row" id="events-section" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex flex-wrap justify-content-between align-items-center gap-3">
                <h5 class="mb-0">{{ __('Events List') }}</h5>
                <div class="d-flex flex-wrap align-items-center justify-content-end gap-2 w-100 w-md-auto">
                    <!-- Bulk Actions (hidden by default) -->
                    <div id="bulk-actions" class="d-flex align-items-center gap-2 flex-nowrap" style="display: none; overflow-x: auto;">
                        <span class="text-muted" id="selected-count" style="white-space: nowrap;">0 selected</span>

                        <select id="bulk-status" class="form-select form-select-sm" style="width: auto; min-width: 140px;">
                            <option value="">{{ __('Change Status') }}</option>
                            <option value="active">{{ __('Active') }}</option>
                            <option value="inactive">{{ __('Inactive') }}</option>
                        </select>
                        <button id="bulk-delete-btn" class="btn btn-danger btn-sm" title="{{ __('Delete Selected') }}">
                            <i class="ti ti-trash"></i>
                        </button>
                    </div>
                    <!-- View Limit Selector -->
                    <div class="d-flex align-items-center gap-2">
                        <label for="events-per-page" class="form-label mb-0">{{ __('Show') }}:</label>
                        <select id="events-per-page" class="form-select form-select-sm" style="width: auto; min-width: 70px;">
                            <option value="5">5</option>
                            <option value="10" selected>10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>

                    <!-- Create Button -->
                    <div class="action-buttons">
                        <button id="create-event-btn" onclick="openCreateEventModal()" 
                            style="background: linear-gradient(to right, #0f5132, #0d6efd); color: white; border: none; padding: 10px 14px; border-radius: 8px; display: inline-flex; align-items: center; font-weight: 600;">
                            <i class="fa fa-plus-circle me-1"></i> {{ __('Create') }}
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="events-table">
                        <thead>
                            <tr>
                                <th width="40" style="min-width: 40px;">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="select-all-events">
                                        <label class="form-check-label" for="select-all-events"></label>
                                    </div>
                                </th>
                                <th style="min-width: 150px;">{{ __('Title') }}</th>
                                <th style="min-width: 120px;">{{ __('Assigned Staff') }}</th>
                                <th style="min-width: 120px;">{{ __('Date Range') }}</th>
                                <th style="min-width: 80px;">{{ __('Duration') }}</th>
                                <th style="min-width: 120px;">{{ __('Location') }}</th>
                                <th style="min-width: 80px;">{{ __('Status') }}</th>
                                <th style="min-width: 100px;">{{ __('Appointments') }}</th>
                                <th style="min-width: 100px;">{{ __('Actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="9" class="text-center">{{ __('Loading events...') }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination Controls -->
                <div class="card-footer d-flex justify-content-between align-items-center" id="events-pagination" style="display: none;">
                    <div class="pagination-info">
                        <span id="pagination-info-text">{{ __('Showing 0 to 0 of 0 entries') }}</span>
                    </div>
                    <nav aria-label="Events pagination">
                        <ul class="pagination pagination-sm mb-0" id="pagination-controls">
                            <!-- Pagination buttons will be generated here -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Appointment Section -->
<!-- Bookings Section -->
<div class="row" id="bookings-section" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex flex-wrap justify-content-between align-items-start gap-3">
                    <h5 class="mb-0">{{ __('All Appointments') }}</h5>

                    @can('create booking')
                    <div class="d-flex flex-wrap align-items-center gap-2 justify-content-start w-100 w-md-auto">

                        <!-- Bulk Actions -->
                        <div id="bulk-booking-actions" class="d-flex align-items-center gap-2" style="display: none;">
                            <span class="text-muted" id="selected-booking-count">0</span>
                            <button id="bulk-cancel-btn" class="btn btn-sm btn-danger" title="{{ __('Cancel Selected Appointments') }}">
                                <i class="ti ti-x me-1"></i>{{ __('Bulk Cancel') }}
                            </button>
                            <div class="vr d-none d-md-block"></div>
                        </div>

                        <!-- Date Range Filter -->
                        <div class="d-flex flex-wrap align-items-center gap-1" style="font-size: 0.875rem;">
                            <label class="form-label mb-0 text-nowrap" style="margin-right: 4px;">{{ __('Date:') }}</label>
                            <input type="date" id="filter-start-date" class="form-control form-control-sm" style="width: 115px; padding: 2px 6px;">
                            <span class="text-muted">–</span>
                            <input type="date" id="filter-end-date" class="form-control form-control-sm" style="width: 115px; padding: 2px 6px;">
                            <button type="button" class="btn btn-sm btn-primary d-flex align-items-center px-2 py-2" onclick="applyDateFilter()" title="Apply Filter">
                                <i class="ti ti-filter"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary px-2 py-1" onclick="clearDateFilter()" title="Clear Filter">
                                {{ __('Clear') }}
                            </button>
                            <div class="vr d-none d-md-block"></div>
                        </div>

                        <!-- Appointment Filters -->
                        <div class="btn-group appointment-filter-group flex-wrap" role="group">
                            <button type="button" class="btn btn-primary btn-sm" id="upcoming-filter-btn" onclick="filterAppointments('upcoming')">
                                <i class="ti ti-calendar-plus me-1"></i>{{ __('Upcoming') }}
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="past-filter-btn" onclick="filterAppointments('past')">
                                <i class="ti ti-calendar-minus me-1"></i>{{ __('Past') }}
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="cancelled-filter-btn" onclick="filterAppointments('cancelled')">
                                <i class="ti ti-calendar-x me-1"></i>{{ __('Cancelled') }}
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="payments-filter-btn" onclick="filterAppointments('payments')">
                                <i class="ti ti-credit-card me-1"></i>{{ __('Payments') }}
                            </button>
                        </div>
                         <div class="vr d-none d-md-block"></div>
                        <!-- Refresh Button -->
                        <button type="button" class="btn btn-sm btn-outline-dark d-flex align-items-center gap-1 px-2 py-1 rounded-2 shadow-sm" onclick="refreshBookings()">
                            <i class="ti ti-refresh"></i>
                            <span class="d-none d-sm-inline">Refresh</span>
                        </button>
                    </div>
                    @endcan
                </div>
            </div>

            <div class="card-body">
                <!-- Alert Messages -->
                <div id="bookings-alert-container" style="display: none;">
                    <div class="alert alert-dismissible fade show" role="alert" id="bookings-alert-message">
                        <span id="bookings-alert-text"></span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>

                <div class="table-responsive" style="overflow-x: auto; white-space: nowrap;">
                    <table class="table table-striped" id="bookings-table" style="min-width: 900px;">
                        <thead>
                            <tr>
                                <th width="40" class="checkbox-column">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="select-all-bookings">
                                        <label class="form-check-label" for="select-all-bookings"></label>
                                    </div>
                                </th>
                                <th style="min-width: 60px;">{{ __('ID') }}</th>
                                <th style="min-width: 150px;">{{ __('Event Title') }}</th>
                                <th style="min-width: 120px;" class="regular-column">{{ __('Assigned Staff') }}</th>
                                <th style="min-width: 120px;">{{ __('Name') }}</th>
                                <th style="min-width: 180px;">{{ __('Email') }}</th>
                                <th style="min-width: 120px;">{{ __('Phone') }}</th>
                                <th style="min-width: 100px;">{{ __('Date') }}</th>
                                <th style="min-width: 80px;">{{ __('Time') }}</th>
                                <th style="min-width: 120px;" class="regular-column">{{ __('Status') }}</th>
                                <th style="min-width: 80px;" class="payment-column" style="display: none;">{{ __('Duration') }}</th>
                                <th style="min-width: 100px;" class="payment-column" style="display: none;">{{ __('Payment Type') }}</th>
                                <th style="min-width: 100px;" class="payment-column" style="display: none;">{{ __('Amount') }}</th>
                                <th style="min-width: 120px;" class="payment-column" style="display: none;">{{ __('Payment Status') }}</th>
                                <th style="min-width: 120px;" class="payment-column" style="display: none;">{{ __('Transaction ID') }}</th>
                                <th style="min-width: 150px;" class="payment-column" style="display: none;">{{ __('Remaining Payment') }}</th>
                                @can('manage booking')
                                <th style="min-width: 100px;" class="actions-column">{{ __('Actions') }}</th>
                                @endcan
                            </tr>
                        </thead>
                        <tbody id="bookings-tbody">
                            <tr>
                                <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '11' : '10' }}" class="text-center py-4">
                                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                    {{ __('Loading bookings...') }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Appointment Booking Modal -->
<div class="modal fade" id="appointmentBookingModal" tabindex="-1" aria-labelledby="appointmentBookingModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="appointmentBookingModalLabel">
                    <i class="ti ti-calendar-plus me-2"></i>{{ __('Book Appointment') }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="appointmentBookingForm">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <!-- Contact Name with CRM Integration -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_contact_name" class="form-label">{{ __('Contact Name') }} *</label>
                            <select class="form-control" id="appointment_contact_name" name="name" required onchange="updateContactDetails()">
                                <option value="">{{ __('Select a contact from CRM') }}</option>
                                <!-- CRM contacts will be populated dynamically -->
                            </select>
                            <small class="text-muted">Select a contact from your CRM or leads</small>
                        </div>

                        <!-- Calendar Event -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_calendar_event" class="form-label">{{ __('Calendar Event') }} *</label>
                            <select class="form-control" id="appointment_calendar_event" name="event_id" required onchange="updateEventDetails()">
                                <option value="">{{ __('Select an event') }}</option>
                                <!-- Events will be populated dynamically -->
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Event Location Type -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_location_type" class="form-label">{{ __('Event Location') }}</label>
                            <select class="form-control" id="appointment_location_type" name="selected_location[type]" onchange="updateLocationValue()">
                                <option value="">{{ __('Select a location') }}</option>
                                <!-- Locations will be populated dynamically -->
                            </select>
                        </div>

                        <!-- Event Location Value -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_location_value" class="form-label">{{ __('Event Location Value') }}</label>
                            <input type="text" class="form-control" id="appointment_location_value" name="selected_location[value]" readonly>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Event Date -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_date" class="form-label">{{ __('Event Date') }} *</label>
                            <input type="date" class="form-control" id="appointment_date" name="date" required>
                        </div>

                        <!-- Time Slot -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_timeslot" class="form-label">{{ __('Time Slot') }} *</label>
                            <select class="form-control" id="appointment_timeslot" name="time" required>
                                <option value="">{{ __('Select a time slot') }}</option>
                                <!-- Time slots will be populated dynamically -->
                            </select>
                        </div>
                    </div>

                    <!-- Payment Section -->
                    <div id="appointment_payment_container" class="mt-3" style="display: none;">
                        <h6 class="text-primary mb-3">{{ __('Payment Information') }}</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">{{ __('Total Amount') }}</label>
                                <div class="input-group">
                                    <span class="input-group-text" id="payment_currency_symbol">$</span>
                                    <input type="text" class="form-control" id="appointment_total_amount" readonly>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3" id="partial_payment_option_container" style="display: none;">
                                <label class="form-label">{{ __('Payment Type') }}</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" id="full_payment" name="payment_type" value="full" checked>
                                    <label class="form-check-label" for="full_payment">
                                        {{ __('Full Payment') }}
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" id="partial_payment" name="payment_type" value="partial">
                                    <label class="form-check-label" for="partial_payment">
                                        {{ __('Partial Payment') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="row" id="partial_payment_details" style="display: none;">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">{{ __('Partial Amount') }}</label>
                                <div class="input-group">
                                    <span class="input-group-text" id="partial_payment_currency_symbol">$</span>
                                    <input type="text" class="form-control" id="appointment_partial_amount" readonly>
                                </div>
                                <small class="text-muted" id="partial_payment_percentage_text"></small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">{{ __('Remaining Amount') }}</label>
                                <div class="input-group">
                                    <span class="input-group-text" id="remaining_payment_currency_symbol">$</span>
                                    <input type="text" class="form-control" id="appointment_remaining_amount" readonly>
                                </div>
                                <small class="text-muted">{{ __('To be paid later') }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- Custom Fields Section (Optional) -->
                    <div id="appointment_custom_fields_container" class="mt-3" style="display: none;">
                        <h6 class="text-primary mb-3">{{ __('Additional Information (Optional)') }}</h6>
                        <div id="appointment_custom_fields_content">
                            <!-- Custom fields will be populated dynamically -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="ti ti-x me-1"></i>{{ __('Cancel') }}
                    </button>
                    <button type="submit" class="btn btn-success" id="appointmentSubmitBtn">
                        <i class="ti ti-check me-1"></i>{{ __('Book Appointment') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Toast Notification for Copy Event Link -->
<div id="copy-event-toast" class="copy-event-toast" role="alert" aria-live="polite" aria-atomic="true" style="display:none;">
    <div class="toast-body d-flex align-items-center">
        <i class="ti ti-check-circle me-2 text-success fs-5"></i>
        <span id="copy-event-toast-message">Event link copied!</span>
        <button type="button" class="btn-close ms-auto" aria-label="Close" onclick="hideCopyEventToast()"></button>
    </div>
</div>

@endsection
@push('script-page')
<script>

//dayslots
// Function to toggle day slots visibility
function toggleDaySlots(day) {
    const slotsDiv = document.getElementById(day + '-slots');
    const dayCard = document.querySelector(`[data-day="${day}"]`);
    
    if (slotsDiv.style.display === 'none') {
        slotsDiv.style.display = 'block';
        dayCard.classList.add('active');
    } else {
        slotsDiv.style.display = 'none';
        dayCard.classList.remove('active');
    }
}

// Function to add time slot
// Function to add time slot
function addTimeSlot(day) {
    const slotsContainer = document.getElementById(day + '-slots');
    const slotCount = slotsContainer.children.length;
    
    const slotHtml = `
        <div class="time-slot">
            <input type="time" name="availability[${day}][slots][${slotCount}][start]" class="form-control" required>
            <span class="mx-2">to</span>
            <input type="time" name="availability[${day}][slots][${slotCount}][end]" class="form-control" required>
            <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('${day}', ${slotCount})">
                <i class="ti ti-trash"></i>
            </button>
        </div>
    `;
    
    slotsContainer.insertAdjacentHTML('beforeend', slotHtml);
}

// Function to remove time slot
function removeTimeSlot(day, slotIndex) {
    const slotsContainer = document.getElementById(day + '-slots');
    const slotToRemove = slotsContainer.children[slotIndex];
    if (slotToRemove) {
        slotToRemove.remove();
    }
}

// Make functions global
window.toggleDaySlots = toggleDaySlots;
window.addTimeSlot = addTimeSlot;
window.removeTimeSlot = removeTimeSlot;
//day slots ends

// Date Range Functions
function handleDateRangeChange() {
    const calendarDaysOption = document.getElementById('calendar_days_option');
    const dateRangeOption = document.getElementById('date_range_option');
    const indefinitelyOption = document.getElementById('indefinitely_option');

    const calendarDaysInput = document.getElementById('calendar_days_input');
    const dateRangeStartInput = document.getElementById('date_range_start_input');
    const dateRangeEndInput = document.getElementById('date_range_end_input');

    // Reset all inputs
    calendarDaysInput.disabled = true;
    dateRangeStartInput.disabled = true;
    dateRangeEndInput.disabled = true;

    // Enable inputs based on selection
    if (calendarDaysOption.checked) {
        calendarDaysInput.disabled = false;
        calendarDaysInput.required = true;
        dateRangeStartInput.required = false;
        dateRangeEndInput.required = false;
    } else if (dateRangeOption.checked) {
        dateRangeStartInput.disabled = false;
        dateRangeEndInput.disabled = false;
        dateRangeStartInput.required = true;
        dateRangeEndInput.required = true;
        calendarDaysInput.required = false;
    } else if (indefinitelyOption.checked) {
        calendarDaysInput.required = false;
        dateRangeStartInput.required = false;
        dateRangeEndInput.required = false;
    }
}

// Function to validate date range inputs
function validateDateRange() {
    const dateRangeOption = document.getElementById('date_range_option');
    const dateRangeStartInput = document.getElementById('date_range_start_input');
    const dateRangeEndInput = document.getElementById('date_range_end_input');
    
    if (dateRangeOption.checked) {
        const startDate = new Date(dateRangeStartInput.value);
        const endDate = new Date(dateRangeEndInput.value);
        const today = new Date();
        
        // Reset time to compare dates only
        today.setHours(0, 0, 0, 0);
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(0, 0, 0, 0);
        
        if (startDate < today) {
            alert('Start date cannot be in the past.');
            dateRangeStartInput.focus();
            return false;
        }
        
        if (endDate < startDate) {
            alert('End date must be after start date.');
            dateRangeEndInput.focus();
            return false;
        }
    }
    
    return true;
}

// Initialize date range event listeners
document.addEventListener('DOMContentLoaded', function() {
    const dateRangeRadios = document.querySelectorAll('input[name="date_range_type"]');
    dateRangeRadios.forEach(radio => {
        radio.addEventListener('change', handleDateRangeChange);
    });

    // Add event listeners for date range inputs
    const dateRangeStartInput = document.getElementById('date_range_start_input');
    const dateRangeEndInput = document.getElementById('date_range_end_input');
    const calendarDaysInput = document.getElementById('calendar_days_input');
    
    if (dateRangeStartInput) {
        dateRangeStartInput.addEventListener('change', validateDateRange);
    }
    if (dateRangeEndInput) {
        dateRangeEndInput.addEventListener('change', validateDateRange);
    }
    if (calendarDaysInput) {
        calendarDaysInput.addEventListener('input', function() {
            const value = parseInt(this.value);
            if (value < 1) {
                this.value = 1;
            } else if (value > 365) {
                this.value = 365;
            }
        });
    }

    // Set initial state
    handleDateRangeChange();
    
    // Initialize column visibility for bookings table
    // Hide payment columns by default since we start with 'upcoming' filter
    applyColumnVisibility();
    console.log('Initialized column visibility for filter:', currentAppointmentFilter);
});

// Make date range function global
window.handleDateRangeChange = handleDateRangeChange;


//appoointment starts


// Function to switch appointment views
function switchAppointmentView(viewType) {
    console.log('Switching to appointment view:', viewType);
    
    // Update buttons
    $('#upcoming-btn, #past-btn, #daterange-btn, #canceled-btn').removeClass('btn-primary active').addClass('btn-outline-primary');
    $('#' + viewType + '-btn').removeClass('btn-outline-primary').addClass('btn-primary active');
    
    // Hide all appointment sections
    $('#upcoming-appointments, #past-appointments, #daterange-appointments, #canceled-appointments').hide();
    
    // Show/hide date range filter
    if (viewType === 'daterange') {
        $('#date-range-filter').show();
        $('#daterange-appointments').show();
    } else {
        $('#date-range-filter').hide();
        $('#' + viewType + '-appointments').show();
    }
    
    // Load data based on view type
    loadAppointmentData(viewType);
}

// Function to load appointment data
function loadAppointmentData(viewType) {
    console.log('Loading appointment data for:', viewType);
    
    if (viewType === 'upcoming') {
        loadUpcomingAppointments();
    } else if (viewType === 'past') {
        loadPastAppointments();
    } else if (viewType === 'canceled') {
        loadCanceledAppointments();
    }
}

// Function to load upcoming appointments
function loadUpcomingAppointments() {
    $.ajax({
        url: '{{ route("calendar-events.index") }}',
        method: 'GET',
        success: function(response) {
            const tbody = $('#upcoming-appointments-tbody');
            tbody.empty();
            
            if (response.success && response.data && response.data.length > 0) {
                const now = new Date();
                const upcomingEvents = response.data.filter(event => {
                    // Add null check for event and required properties
                    if (!event || !event.start_date || !event.id) {
                        console.warn('Invalid upcoming event data:', event);
                        return false;
                    }
                    const eventDate = new Date(event.start_date);
                    return eventDate > now; // Only future events
                });

                if (upcomingEvents.length > 0) {
                    upcomingEvents.forEach(function(event) {
                        // Additional safety check
                        if (!event || !event.id) {
                            console.warn('Skipping invalid upcoming event:', event);
                            return;
                        }
                        const startDate = new Date(event.start_date);
                        const formattedDate = startDate.toLocaleDateString() + ' ' + startDate.toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                        
                        const row = `
                            <tr>
                                <td>${event.title}</td>
                                <td>${formattedDate}</td>
                                <td>${event.duration || 60} min</td>
                                <td><span class="badge bg-success">Upcoming</span></td>
                                <td>
                                    <button class="btn btn-sm btn-info me-1" onclick="viewEvent(${event.id})" title="View Details">
                                        <i class="ti ti-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-warning me-1" onclick="editEvent(${event.id})" title="Edit">
                                        <i class="ti ti-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteEvent(${event.id})" title="Cancel">
                                        <i class="ti ti-x"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                        tbody.append(row);
                    });
                } else {
                    tbody.html('<tr><td colspan="5" class="text-center text-muted">No upcoming appointments found</td></tr>');
                }
            } else {
                tbody.html('<tr><td colspan="5" class="text-center text-muted">No upcoming appointments found</td></tr>');
            }
        },
        error: function(xhr) {
            console.log('Error loading upcoming appointments:', xhr.responseText);
            $('#upcoming-appointments-tbody').html('<tr><td colspan="5" class="text-center text-danger">Error loading appointments</td></tr>');
        }
    });
}

// Function to load past appointments
function loadPastAppointments() {
    $.ajax({
        url: '{{ route("calendar-events.index") }}',
        method: 'GET',
        success: function(response) {
            const tbody = $('#past-appointments-tbody');
            tbody.empty();
            
            if (response.success && response.data && response.data.length > 0) {
                const now = new Date();
                const pastEvents = response.data.filter(event => {
                    // Add null check for event and required properties
                    if (!event || !event.end_date || !event.id) {
                        console.warn('Invalid past event data:', event);
                        return false;
                    }
                    const eventDate = new Date(event.end_date);
                    return eventDate < now; // Only past events
                });

                if (pastEvents.length > 0) {
                    pastEvents.forEach(function(event) {
                        // Additional safety check
                        if (!event || !event.id) {
                            console.warn('Skipping invalid past event:', event);
                            return;
                        }
                        const startDate = new Date(event.start_date);
                        const formattedDate = startDate.toLocaleDateString() + ' ' + startDate.toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                        
                        const row = `
                            <tr>
                                <td>${event.title}</td>
                                <td>${formattedDate}</td>
                                <td>${event.duration || 60} min</td>
                                <td><span class="badge bg-secondary">Completed</span></td>
                                <td>
                                    <button class="btn btn-sm btn-info" onclick="viewEvent(${event.id})" title="View Details">
                                        <i class="ti ti-eye"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                        tbody.append(row);
                    });
                } else {
                    tbody.html('<tr><td colspan="5" class="text-center text-muted">No past appointments found</td></tr>');
                }
            } else {
                tbody.html('<tr><td colspan="5" class="text-center text-muted">No past appointments found</td></tr>');
            }
        },
        error: function(xhr) {
            console.log('Error loading past appointments:', xhr.responseText);
            $('#past-appointments-tbody').html('<tr><td colspan="5" class="text-center text-danger">Error loading appointments</td></tr>');
        }
    });
}

function loadCanceledAppointments() {
    console.log('Loading canceled appointments...');
    const tbody = $('#bookings-tbody');
    tbody.html(`
        <tr>
            <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '11' : '10' }}" class="text-center py-4">
                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                {{ __('Loading canceled appointments...') }}
            </td>
        </tr>
    `);

    $.ajax({
        url: '{{ route("bookings.index") }}',
        method: 'GET',
        data: {
            status: 'canceled'
        },
        success: function(response) {
            tbody.empty();

            try {
                let $response = $(response);
                let $bookingRows = $response.find('#bookings-table tbody tr');

                if ($bookingRows.length > 0) {
                    // Check if it's the "no bookings" row
                    let firstRowText = $bookingRows.first().text().trim();
                    if (firstRowText.includes('No bookings found') || firstRowText.includes('{{ __("No bookings found") }}')) {
                        tbody.html(`
                            <tr>
                                <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '11' : '10' }}" class="text-center">{{ __('No canceled appointments found') }}</td>
                            </tr>
                        `);
                    } else {
                        $bookingRows.each(function() {
                            tbody.append($(this));
                        });
                    }
                } else {
                    tbody.html(`
                        <tr>
                            <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '11' : '10' }}" class="text-center">{{ __('No canceled appointments found') }}</td>
                        </tr>
                    `);
                }
            } catch (error) {
                console.error('Error parsing canceled bookings:', error);
                tbody.html(`
                    <tr>
                        <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '11' : '10' }}" class="text-center text-danger">{{ __('Error loading canceled appointments') }}</td>
                    </tr>
                `);
            }
        },
        error: function(xhr) {
            console.error('Error loading canceled appointments:', xhr);
            tbody.html(`
                <tr>
                    <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '11' : '10' }}" class="text-center text-danger">{{ __('Error loading canceled appointments') }}</td>
                </tr>
            `);
        }
    });
}

// Make functions global
window.switchAppointmentView = switchAppointmentView;
window.applyColumnVisibility = applyColumnVisibility;

//appointment ends


let calendar;
let editingEventId = null;
let allEvents = [];

// Function to load all events and store them
function loadAllEvents() {
    $.ajax({
        url: '{{ route("calendar-events.index") }}',
        method: 'GET',
        success: function(response) {
            if (response.success && response.data) {
                allEvents = response.data;
                console.log('All events loaded:', allEvents);
                
                // Show today's events by default
                const today = new Date();
                const dayName = today.toLocaleDateString('en-US', { weekday: 'long' });
                updateSelectedDateContainer(today, dayName);
            }
        },
        error: function(xhr) {
            console.log('Error loading events:', xhr.responseText);
        }
    });
}

// Function to update the selected date container
function updateSelectedDateContainer(date, dayName) {
    console.log('Updating sidebar for:', dayName, date);
    
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };
    
    const formattedDate = date.toLocaleDateString('en-US', options);
    
    $('#selected-date-title').text(dayName);
    $('#selected-date-subtitle').text(formattedDate);
    
    // Show events for selected date
    showEventsForDate(date);
}

// Function to show events for a specific date
function showEventsForDate(selectedDate) {
    console.log('Showing events for:', selectedDate);
    
    const dateStr = selectedDate.toISOString().split('T')[0];
    
    // Filter events for the selected date
    const dayEvents = allEvents.filter(event => {
        const eventDate = new Date(event.start_date).toISOString().split('T')[0];
        return eventDate === dateStr;
    });
    
    console.log('Found events:', dayEvents);
    
    const eventsList = $('#events-list');
    const noEventsMessage = $('#no-events-message');
    
    if (dayEvents.length > 0) {
        noEventsMessage.hide();
        eventsList.show();
        eventsList.empty();
        
        dayEvents.forEach(event => {
            const startTime = new Date(event.start_date).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit'
            });
            
            const endTime = new Date(event.end_date).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit'
            });
            
            // Format location display for sidebar
            let sidebarLocationDisplay = '';
            if (event.locations_data) {
                try {
                    const locations = JSON.parse(event.locations_data);
                    if (locations && locations.length > 0) {
                        if (locations.length === 1) {
                            sidebarLocationDisplay = `<div class="small text-success"><i class="ti ti-map-pin me-1"></i>${locations[0].display}</div>`;
                        } else {
                            sidebarLocationDisplay = `<div class="small text-success"><i class="ti ti-map-pin me-1"></i>${locations[0].display} <span class="badge bg-primary location-count-badge ms-1">+${locations.length - 1}</span></div>`;
                        }
                    }
                } catch (e) {
                    console.error('Error parsing locations_data for sidebar:', e);
                    sidebarLocationDisplay = event.location ? `<div class="small text-success"><i class="ti ti-map-pin me-1"></i>${event.location}</div>` : '';
                }
            } else if (event.location) {
                sidebarLocationDisplay = `<div class="small text-success"><i class="ti ti-map-pin me-1"></i>${event.location}</div>`;
            }

            const eventHtml = `
                <li class="mb-3 p-3 border rounded bg-light">
                    <h6 class="mb-1 text-primary">${event.title}</h6>
                    <div class="small text-muted mb-2">
                        <i class="ti ti-clock me-1"></i>${startTime} - ${endTime}
                    </div>
                    ${event.description ? `<div class="small mb-2">${event.description}</div>` : ''}
                    ${sidebarLocationDisplay}
                </li>
            `;
            eventsList.append(eventHtml);
        });
    } else {
        eventsList.hide();
        noEventsMessage.show();
        noEventsMessage.html(`
            <i class="ti ti-calendar-off fs-1 mb-2 d-block text-muted"></i>
            <div class="text-muted">No events scheduled for this date</div>
        `);
    }
}




// Function to collect availability data
function collectAvailabilityData() {
    const availability = {};

    // Days of the week
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

    days.forEach(function(day) {
        const dayCheckbox = $(`input[name="availability[${day}][enabled]"]`);
        const isEnabled = dayCheckbox.is(':checked');

        availability[day] = {
            enabled: isEnabled,
            slots: []
        };

        if (isEnabled) {
            // Collect time slots for this day
            $(`input[name^="availability[${day}][slots]"][name$="[start]"]`).each(function(index) {
                const startTime = $(this).val();
                const endTime = $(`input[name="availability[${day}][slots][${index}][end]"]`).val();

                if (startTime && endTime) {
                    availability[day].slots.push({
                        start: startTime,
                        end: endTime
                    });
                }
            });
        }
        // Note: For disabled days, we still include them with enabled: false and empty slots array
        // This prevents "Undefined array key 'slots'" errors in the backend
    });

    console.log('Collected availability data:', availability);
    return availability;
}

// Function to populate weekly availability data when editing
function populateWeeklyAvailability(weeklyAvailabilityData) {
    console.log('Populating weekly availability:', weeklyAvailabilityData);

    // Days of the week
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

    // Reset all days first
    days.forEach(function(day) {
        // Uncheck the day checkbox
        $(`#${day}-checkbox`).prop('checked', false);

        // Hide the slots container
        $(`#${day}-slots`).hide();

        // Clear existing slots
        $(`#${day}-slots`).empty();
    });

    // If no weekly availability data, use default (all days enabled with 9-5 slot)
    if (!weeklyAvailabilityData || typeof weeklyAvailabilityData !== 'object') {
        console.log('No weekly availability data found, using defaults');
        days.forEach(function(day) {
            // Check the day checkbox
            $(`#${day}-checkbox`).prop('checked', true);

            // Show the slots container
            $(`#${day}-slots`).show();

            // Add default slot (9:00 - 17:00)
            const slotHtml = `
                <div class="time-slot">
                    <input type="time" name="availability[${day}][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[${day}][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('${day}', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            `;
            $(`#${day}-slots`).html(slotHtml);
        });
        return;
    }

    // Populate with actual data
    days.forEach(function(day) {
        const dayData = weeklyAvailabilityData[day];

        if (dayData && dayData.enabled) {
            // Check the day checkbox
            $(`#${day}-checkbox`).prop('checked', true);

            // Show the slots container
            $(`#${day}-slots`).show();

            // Add slots for this day
            if (dayData.slots && Array.isArray(dayData.slots) && dayData.slots.length > 0) {
                let slotsHtml = '';
                dayData.slots.forEach(function(slot, index) {
                    slotsHtml += `
                        <div class="time-slot">
                            <input type="time" name="availability[${day}][slots][${index}][start]" class="form-control" value="${slot.start || '09:00'}" required>
                            <span class="mx-2">to</span>
                            <input type="time" name="availability[${day}][slots][${index}][end]" class="form-control" value="${slot.end || '17:00'}" required>
                            <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('${day}', ${index})">
                                <i class="ti ti-trash"></i>
                            </button>
                        </div>
                    `;
                });
                $(`#${day}-slots`).html(slotsHtml);
            } else {
                // Day is enabled but no slots, add default slot
                const slotHtml = `
                    <div class="time-slot">
                        <input type="time" name="availability[${day}][slots][0][start]" class="form-control" value="09:00" required>
                        <span class="mx-2">to</span>
                        <input type="time" name="availability[${day}][slots][0][end]" class="form-control" value="17:00" required>
                        <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('${day}', 0)">
                            <i class="ti ti-trash"></i>
                        </button>
                    </div>
                `;
                $(`#${day}-slots`).html(slotHtml);
            }
        } else {
            // Day is disabled - checkbox remains unchecked and slots remain hidden
            console.log(`Day ${day} is disabled`);
        }
    });

    console.log('Weekly availability population completed');
}

// Minimum Notice Calculation Functions
function calculateMinimumNoticeInMinutes() {
    const value = parseInt($('#minimum_notice_value').val()) || 0;
    const unit = $('#minimum_notice_unit').val();

    let minutes = 0;
    switch (unit) {
        case 'minutes':
            minutes = value;
            break;
        case 'hours':
            minutes = value * 60;
            break;
        case 'days':
            minutes = value * 60 * 24;
            break;
        case 'months':
            minutes = value * 60 * 24 * 30; // Approximate 30 days per month
            break;
        default:
            minutes = 0;
    }

    // Update the hidden field
    $('#minimum_notice').val(minutes);

    // Update the preview text
    updateMinimumNoticePreview(value, unit);

    return minutes;
}

function setMinimumNoticeFromMinutes(totalMinutes) {
    totalMinutes = parseInt(totalMinutes) || 0;

    if (totalMinutes === 0) {
        $('#minimum_notice_value').val(0);
        $('#minimum_notice_unit').val('minutes');
        updateMinimumNoticePreview(0, 'minutes');
        return;
    }

    // Convert to the most appropriate unit
    if (totalMinutes >= 43200) { // 30 days or more
        const months = Math.floor(totalMinutes / (60 * 24 * 30));
        $('#minimum_notice_value').val(months);
        $('#minimum_notice_unit').val('months');
        updateMinimumNoticePreview(months, 'months');
    } else if (totalMinutes >= 1440) { // 1 day or more
        const days = Math.floor(totalMinutes / (60 * 24));
        $('#minimum_notice_value').val(days);
        $('#minimum_notice_unit').val('days');
        updateMinimumNoticePreview(days, 'days');
    } else if (totalMinutes >= 60) { // 1 hour or more
        const hours = Math.floor(totalMinutes / 60);
        $('#minimum_notice_value').val(hours);
        $('#minimum_notice_unit').val('hours');
        updateMinimumNoticePreview(hours, 'hours');
    } else {
        $('#minimum_notice_value').val(totalMinutes);
        $('#minimum_notice_unit').val('minutes');
        updateMinimumNoticePreview(totalMinutes, 'minutes');
    }
}

function updateMinimumNoticePreview(value, unit) {
    const previewElement = $('#minimum_notice_preview');

    if (value === 0 || !value) {
        previewElement.text('No minimum notice required');
        return;
    }

    let previewText = '';
    if (value === 1) {
        // Singular form
        switch (unit) {
            case 'minutes':
                previewText = '1 minute advance notice required';
                break;
            case 'hours':
                previewText = '1 hour advance notice required';
                break;
            case 'days':
                previewText = '1 day advance notice required';
                break;
            case 'months':
                previewText = '1 month advance notice required';
                break;
        }
    } else {
        // Plural form
        previewText = `${value} ${unit} advance notice required`;
    }

    previewElement.text(previewText);
}

$(document).ready(function() {
    console.log('Document ready - initializing...');

    // Validate that required modal elements exist
    const requiredModalElements = [
        '#viewEventModal', '#view-event-title',
        '#view-event-duration', '#view-event-booking-slots',
        '#view-event-notice'
    ];

    let missingElements = [];
    requiredModalElements.forEach(elementId => {
        if ($(elementId).length === 0) {
            missingElements.push(elementId);
        }
    });

    if (missingElements.length > 0) {
        console.error('Missing required modal elements:', missingElements);
    }

    // Initialize calendar
    initializeCalendar();

    // Load all events for the sidebar
    loadAllEvents();
    
    // Load events for filter dropdown
    loadEventsForFilter();
    
    // Load staff for filter dropdown
    loadStaffForFilter();
    
    // Add event listeners for filter dropdowns
    $('#event-filter').on('change', function() {
        refreshCalendar();
    });
    
    $('#staff-filter').on('change', function() {
        refreshCalendar();
    });

    // Add event listeners for minimum notice calculation
    $('#minimum_notice_value, #minimum_notice_unit').on('change input', function() {
        calculateMinimumNoticeInMinutes();
    });

    // Initialize minimum notice calculation
    calculateMinimumNoticeInMinutes();
    
    // Payment settings event listeners
    $('#payment_required').on('change', function() {
        const isChecked = $(this).is(':checked');
        $('#payment_amount, #payment_currency').prop('disabled', !isChecked);
        
        if (!isChecked) {
            $('#payment_amount').val('');
            // Hide partial payment settings when payment is disabled
            $('#partial_payment_settings').hide();
            $('#partial_payment_enabled').prop('checked', false);
            $('#partial_payment_percentage').prop('disabled', true).val('');
        } else {
            // Show partial payment settings when payment is enabled
            $('#partial_payment_settings').show();
        }
    });
    
    // Partial payment settings event listeners
    $('#partial_payment_enabled').on('change', function() {
        const isChecked = $(this).is(':checked');
        $('#partial_payment_percentage').prop('disabled', !isChecked);
        
        if (!isChecked) {
            $('#partial_payment_percentage').val('');
        }
    });
    
    // Form submission handler
    $('#createEventForm').on('submit', function(e) {
        e.preventDefault();
        console.log('Form submitted!'); // Check if this log appears
        console.log('editingEventId at form submission:', editingEventId); // Debug log
        
        // Validate date range before submission
        if (!validateDateRange()) {
            return;
        }
        
        const submitBtn = $('#submitBtn');
        const originalText = submitBtn.text();
        
        // Show loading with spinner
        submitBtn.prop('disabled', true).html(editingEventId ? 
            '<i class="ti ti-loader ti-spin me-1"></i>Updating...' : 
            '<i class="ti ti-loader ti-spin me-1"></i>Creating...'
        );
        
        // Collect custom fields data with placeholders and required settings
        const customFieldsData = [];

        try {
            $('#custom-fields-container .custom-field-row').each(function() {
                const fieldId = $(this).data('field-id');
                const fieldName = $(this).data('field-name');
                if (fieldId) {
                    const placeholder = $(`#custom-field-${fieldId}`).val() || `Enter ${fieldName.toLowerCase()}`;
                    const isRequired = $(`#custom_field_required_${fieldId}`).is(':checked');
                    
                    customFieldsData.push({
                        id: fieldId,
                        name: fieldName,
                        placeholder: placeholder,
                        required: isRequired
                    });
                }
            });
        } catch (error) {
            console.log('Error collecting custom fields:', error);
        }

        // Get form data
        const formData = {
            title: $('#event_title').val(),
            duration: $('#event_duration').val() || 60,
            booking_per_slot: $('#booking_per_slot').val() || 1,
            minimum_notice: $('#minimum_notice').val() || 0,
            minimum_notice_value: $('#minimum_notice_value').val() || 0,
            minimum_notice_unit: $('#minimum_notice_unit').val() || 'minutes',
            description: $('#event_description').summernote('code'),
            payment_required: $('#payment_required').is(':checked'),
            payment_amount: $('#payment_required').is(':checked') ? $('#payment_amount').val() : null,
            payment_currency: $('#payment_required').is(':checked') ? $('#payment_currency').val() : 'USD',
            partial_payment_enabled: $('#payment_required').is(':checked') && $('#partial_payment_enabled').is(':checked'),
            partial_payment_percentage: $('#payment_required').is(':checked') && $('#partial_payment_enabled').is(':checked') ? $('#partial_payment_percentage').val() : null,
            locations_data: $('#event_locations_data').val(),
            custom_redirect_url: $('#custom_redirect_url').val(),
            assigned_staff_id: $('#assigned_staff_id').val() || null,
            // Legacy fields for backward compatibility
            location: selectedLocations.length > 0 ? selectedLocations[0].type : '',
            meet_link: selectedLocations.find(loc => ['zoom', 'meet', 'skype', 'phone', 'others'].includes(loc.type))?.value || '',
            physical_address: selectedLocations.find(loc => loc.type === 'in_person')?.value || '',
            require_name: $('#require_name').is(':checked'),
            require_email: $('#require_email').is(':checked'),
            require_phone: $('#require_phone').is(':checked'),
            custom_field: $('#custom_field').val(), // Single field for backward compatibility
            custom_fields: customFieldsData, // Complete custom fields data with placeholders and required settings
            // Date Range fields
            date_range_type: $('input[name="date_range_type"]:checked').val() || 'indefinitely',
            date_range_days: $('#calendar_days_input').val() || null,
            date_range_start: $('#date_range_start_input').val() || null,
            date_range_end: $('#date_range_end_input').val() || null,
            // Status field
            status: $('#event_status').val() || 'active',
            availability: (function() {
                try {
                    return collectAvailabilityData();
                } catch (error) {
                    console.log('Error collecting availability data:', error);
                    return {};
                }
            })(),
            date_override: (function() {
                try {
                    return collectDateOverrideData();
                } catch (error) {
                    console.log('Error collecting date override data:', error);
                    return {};
                }
            })(),
            _token: $('input[name="_token"]').val()
        };

        // Add _method field for PUT request when updating
        if (editingEventId) {
            formData._method = 'PUT';
        }

        console.log('Sending data:', formData); // Log form data
        console.log('Date Range Type:', formData.date_range_type);
        console.log('Date Range Days:', formData.date_range_days);
        console.log('Date Range Start:', formData.date_range_start);
        console.log('Date Range End:', formData.date_range_end);

        // Determine URL - always use POST but add _method for PUT
        const url = editingEventId ?
            `{{ url('calendar-events') }}/${editingEventId}` :
            '{{ route("calendar-events.store") }}';

        console.log('AJAX URL:', url); // Log the URL
        console.log('Editing Event ID:', editingEventId); // Log editing state
        
        // Send AJAX request
        $.ajax({
            url: url,
            method: 'POST',
            data: formData,
            timeout: 30000, // 30 second timeout
            success: function(response) {
                console.log('Success response:', response);
                
                // Reset button
                submitBtn.prop('disabled', false).html(originalText);
                
                if (response.success) {
                    // Close modal
                    $('#createEventModal').modal('hide');
                    
                    // Reset form
                    resetForm();
                    
                    // Show success toast message
                    console.log('editingEventId at success:', editingEventId); // Debug log
                    const successMessage = editingEventId ? 
                        'Event updated successfully!' : 
                        'Event created successfully! Appointment slots are being generated in the background.';
                    show_toastr('success', successMessage);
                    
                    // Reset editing state
                    editingEventId = null;
                    
                    // Refresh calendar and events
                    if (calendar) {
                        calendar.refetchEvents();
                    }
                    loadEventsList(currentPage, currentPerPage);
                    loadAllEvents(); // Refresh sidebar events
                } else {
                    console.log('Validation errors:', response.errors); // Log validation errors
                    show_toastr('error', response.message || 'Unknown error');
                }
            },
            error: function(xhr, status, error) {
                console.log('Error response:', xhr.responseText);
                console.log('Error status:', status);
                
                // Reset button
                submitBtn.prop('disabled', false).html(originalText);
                
                let errorMessage = 'An error occurred';
                
                // Handle timeout specifically
                if (status === 'timeout') {
                    errorMessage = 'Request timed out. The event may have been created successfully. Please refresh the page to check.';
                    show_toastr('warning', errorMessage);
                    return;
                }
                
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    if (errorResponse.errors) {
                        errorMessage = 'Validation errors:\n';
                        Object.keys(errorResponse.errors).forEach(key => {
                            errorMessage += `${key}: ${errorResponse.errors[key].join(', ')}\n`;
                        });
                    } else if (errorResponse.message) {
                        errorMessage = errorResponse.message;
                    }
                } catch (e) {
                    errorMessage = `Server Error (${xhr.status}): ${xhr.responseText || 'Unknown error'}`;
                }
                
                show_toastr('error', errorMessage);
            }
        });
    });
});
   

// Load events for filter dropdown
function loadEventsForFilter() {
    $.ajax({
        url: "{{ route('calendar-events.events-for-filter') }}",
        method: "GET",
        success: function(response) {
            if (response.success) {
                const eventFilter = $('#event-filter');
                eventFilter.empty();
                eventFilter.append('<option value="">{{ __("All Events") }}</option>');
                
                response.events.forEach(function(event) {
                    eventFilter.append(`<option value="${event.id}">${event.title}</option>`);
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading events for filter:', error);
        }
    });
}

// Load staff for filter dropdown
function loadStaffForFilter() {
    $.ajax({
        url: "{{ route('calendar-events.staff-for-filter') }}",
        method: "GET",
        success: function(response) {
            if (response.success) {
                const staffFilter = $('#staff-filter');
                staffFilter.empty();
                staffFilter.append('<option value="">{{ __("All Staff") }}</option>');
                
                response.staff.forEach(function(staff) {
                    staffFilter.append(`<option value="${staff.id}">${staff.name} (${staff.type})</option>`);
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading staff for filter:', error);
        }
    });
}

// Note: clearAllFilters function removed as the clear button was removed from UI

// View appointment booking details
function viewAppointmentBooking(appointmentId) {
    $.ajax({
        url: `{{ url('appointment-bookings') }}/${appointmentId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const appointment = response.data;
                
                let content = `
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <h6 class="card-title text-primary"><i class="ti ti-user me-2"></i>Contact Information</h6>
                                    <p><strong>Name:</strong> ${appointment.name || 'N/A'}</p>
                                    <p><strong>Email:</strong> ${appointment.email || 'N/A'}</p>
                                    <p><strong>Phone:</strong> ${appointment.phone || 'N/A'}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card border-success">
                                <div class="card-body">
                                    <h6 class="card-title text-success"><i class="ti ti-calendar me-2"></i>Appointment Details</h6>
                                    <p><strong>Event:</strong> ${appointment.event_title || 'N/A'}</p>
                                    <p><strong>Date:</strong> ${appointment.event_date || 'N/A'}</p>
                                    <p><strong>Time:</strong> ${appointment.time_slots || 'N/A'}</p>
                                    <p><strong>Location:</strong> ${appointment.event_location_value || 'N/A'}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Show in modal
                $('#viewEventModal .modal-title').text('Appointment Details');
                $('#viewEventModal .modal-body').html(content);
                $('#viewEventModal').modal('show');
            } else {
                show_toastr('error', 'Failed to load appointment details');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading appointment details:', error);
            show_toastr('error', 'Failed to load appointment details');
        }
    });
}

// Refresh calendar with current filter
function refreshCalendar() {
    if (calendar) {
        calendar.refetchEvents();
    }
}

// Initialize calendar
function initializeCalendar() {
    console.log('Initializing calendar...');
    
    if (typeof FullCalendar === 'undefined') {
        console.error('FullCalendar not loaded');
        return;
    }
    
    const calendarEl = document.getElementById('calendar');
    if (!calendarEl) {
        console.error('Calendar element not found');
        return;
    }
    
    try {
        calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            buttonText: {
                today: "{{ __('Today') }}",
                month: "{{ __('Month') }}",
                week: "{{ __('Week') }}",
                day: "{{ __('Day') }}"
            },
            height: 'auto',
            selectable: false,
            selectMirror: false,
            // Performance optimizations
            lazyFetching: true,
            eventDisplay: 'block',
            dayMaxEvents: 5,
            moreLinkClick: 'popover',
            // Prevent scroll blocking
            scrollTime: '00:00:00',
            scrollTimeReset: false,
            // Optimize rendering
            eventTimeFormat: {
                hour: 'numeric',
                minute: '2-digit',
                meridiem: 'short'
            },
            events: function(fetchInfo, successCallback, failureCallback) {
                const eventFilter = $('#event-filter').val();
                const staffFilter = $('#staff-filter').val();
                
                // Get date range from FullCalendar's fetchInfo
                const startDate = fetchInfo.startStr;
                const endDate = fetchInfo.endStr;
                
                $.ajax({
                    url: "{{ route('calendar-events.calendar-data') }}",
                    method: "POST",
                    data: {
                        "_token": "{{ csrf_token() }}",
                        "event_filter": eventFilter,
                        "staff_filter": staffFilter,
                        "start": startDate,
                        "end": endDate
                    },
                    success: function(data) {
                        console.log('Calendar data loaded:', data);
                        successCallback(data);
                    },
                    error: function(xhr, status, error) {
                        console.log('Error loading calendar data:', error);
                        console.log('Response:', xhr.responseText);
                        failureCallback(error);
                    }
                });
            },
            // select: function(info) {
            //     // This function is disabled to prevent interference with dateClick
            //     // If you need to create events, use the "Create Event" button instead
            // },
            eventClick: function(info) {
                // Add these lines:
                if (info.jsEvent) {
                    info.jsEvent.stopPropagation();
                    info.jsEvent.preventDefault();
                }
                // ...existing code...
                if (!info || !info.event) {
                    console.warn('Invalid event click info:', info);
                    return;
                }

                const eventId = info.event.id;
                console.log('Calendar event clicked, raw ID:', eventId);

                if (eventId) {
                    // Handle booking clicks
                    if (typeof eventId === 'string' && eventId.startsWith('booking_')) {
                        const bookingId = eventId.replace('booking_', '');
                        console.log('Booking clicked, ID:', bookingId);
                        viewBooking(parseInt(bookingId));
                        return;
                    }

                    // Handle appointment booking clicks
                    if (typeof eventId === 'string' && eventId.startsWith('appointment_')) {
                        const appointmentId = eventId.replace('appointment_', '');
                        console.log('Appointment booking clicked, ID:', appointmentId);
                        viewAppointmentBooking(parseInt(appointmentId));
                        return;
                    }

                    // Handle regular event clicks
                    if (typeof eventId === 'string' && eventId.startsWith('event_')) {
                        const numericId = eventId.replace('event_', '');
                        console.log('Event clicked, ID:', numericId);
                        viewEvent(parseInt(numericId));
                        return;
                    }

                    console.error('Unknown event type:', eventId);
                }
            },
            dateClick: function(info) {
                console.log('Date clicked:', info);

                const clickedDate = new Date(info.date);
                const formattedDate = clickedDate.toISOString().split('T')[0];

                // Set the selected date in the appointment form
                // Load available events
                loadAvailableEvents();

                // Show the appointment booking modal
                $('#appointmentBookingModal').modal('show');
            }
        });
        
        calendar.render();
        console.log('Calendar initialized successfully');
        
    } catch (error) {
        console.error('Error initializing calendar:', error);
    }
}


//custom field
let customFieldCounter = 0;

function toggleCustomField() {
    // This function is now just for compatibility
    // The actual functionality is handled by addCustomField()
}

function addCustomField() {
    const selectedOption = $('#custom_field option:selected');
    const fieldId = selectedOption.val();
    const fieldName = selectedOption.text();

    // Don't add if no field is selected
    if (!fieldId) {
        alert('Please select a field type first');
        return;
    }

    // Check if this field already exists
    if ($(`#custom-field-${fieldId}`).length > 0) {
        alert('This field has already been added');
        return;
    }

    customFieldCounter++;
    const inputId = `custom-field-${fieldId}`;

    // Create a proper input field with placeholder and required option
    const fieldHtml = `
        <div class="row mb-3 custom-field-row" data-field-id="${fieldId}" data-field-name="${fieldName}">
            <div class="col-12">
                <div class="card border-primary">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">${fieldName}</label>
                                <input type="text" class="form-control readonly-placeholder" id="${inputId}" 
                                       name="custom_field_placeholder[${fieldId}]" 
                                       placeholder="Enter placeholder text for ${fieldName.toLowerCase()}"
                                       value="Enter ${fieldName.toLowerCase()}" readonly>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           id="custom_field_required_${fieldId}" 
                                           name="custom_field_required[${fieldId}]" value="1">
                                    <label class="form-check-label" for="custom_field_required_${fieldId}">
                                        <i class="ti ti-asterisk me-1 text-danger"></i>{{ __('Is Required') }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeCustomField('${fieldId}')">
                                    <i class="ti ti-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="ti ti-info-circle me-1"></i>
                                This custom field will be available during booking
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add the field to the container
    $('#custom-fields-container').append(fieldHtml);

    // Reset the dropdown
    $('#custom_field').val('');
}

function removeCustomField(fieldId) {
    $(`.custom-field-row[data-field-id="${fieldId}"]`).remove();
}

// Function to populate custom fields when editing an event
function populateCustomFieldsForEdit(event) {
    // Clear existing custom fields
    $('#custom-fields-container').empty();
    customFieldCounter = 0;

    // Check if event has custom fields
    if (event.custom_fields && Array.isArray(event.custom_fields)) {
        event.custom_fields.forEach(function(field) {
            if (field.id && field.name) {
                // Add the custom field to the form
                addCustomFieldForEdit(field.id, field.name);
            }
        });
    }
}

// Function to add a custom field during edit (similar to addCustomField but for editing)
function addCustomFieldForEdit(fieldId, fieldName, placeholder = '', isRequired = false) {
    // Check if this field already exists
    if ($(`#custom-field-${fieldId}`).length > 0) {
        return; // Field already exists
    }

    customFieldCounter++;
    const inputId = `custom-field-${fieldId}`;

    // Create a proper input field with placeholder and required option for editing
    const fieldHtml = `
        <div class="row mb-3 custom-field-row" data-field-id="${fieldId}" data-field-name="${fieldName}">
            <div class="col-12">
                <div class="card border-primary">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">${fieldName}</label>
                                <input type="text" class="form-control readonly-placeholder" id="${inputId}" 
                                       name="custom_field_placeholder[${fieldId}]" 
                                       placeholder="Enter placeholder text for ${fieldName.toLowerCase()}"
                                       value="${placeholder || `Enter ${fieldName.toLowerCase()}`}" readonly>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           id="custom_field_required_${fieldId}" 
                                           name="custom_field_required[${fieldId}]" value="1"
                                           ${isRequired ? 'checked' : ''}>
                                    <label class="form-check-label" for="custom_field_required_${fieldId}">
                                        <i class="ti ti-asterisk me-1 text-danger"></i>{{ __('Is Required') }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeCustomField('${fieldId}')">
                                    <i class="ti ti-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="ti ti-info-circle me-1"></i>
                                This custom field will be available during booking
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add the field to the container
    $('#custom-fields-container').append(fieldHtml);
}

// Appointment Booking Functions
function loadAvailableEvents() {
    console.log('Loading available events');

    // Clear previous options
    $('#appointment_calendar_event').html('<option value="">{{ __("Select an event") }}</option>');
    $('#appointment_timeslot').html('<option value="">{{ __("Select a time slot") }}</option>');
    $('#appointment_location_type').val('');
    $('#appointment_location_value').val('');

    // Make AJAX call to get all available events
    $.ajax({
        url: '{{ route("calendar-events.index") }}',
        method: 'GET',
        success: function(response) {
            if (response.success && response.data.length > 0) {
                // Store events globally for time slot generation
                window.availableEvents = response.data;

                response.data.forEach(function(event) {
                    // Add null check for event and required properties
                    if (!event || !event.id || !event.title) {
                        console.warn('Skipping invalid event in appointment dropdown:', event);
                        return;
                    }

                    // Determine location value based on location type
                    let locationValue = '';
                    if (event.location === 'in_person') {
                        locationValue = event.physical_address || 'Physical address not specified';
                    } else {
                        locationValue = event.meet_link || 'Meeting link not specified';
                    }

                    $('#appointment_calendar_event').append(
                        `<option value="${event.id}" data-location="${event.location || ''}" data-location-value="${locationValue}" data-duration="${event.duration || 60}">
                            ${event.title} (${event.duration || 60} min)
                        </option>`
                    );
                });
            } else {
                window.availableEvents = [];
                $('#appointment_calendar_event').append('<option value="" disabled>{{ __("No events available for this date") }}</option>');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading events:', error);
            window.availableEvents = [];
            alert('Error loading events for the selected date');
        }
    });
}

function updateEventDetails() {
    const selectedOption = $('#appointment_calendar_event option:selected');
    const eventId = selectedOption.val();

    if (eventId) {
        // Get event data from the global events array
        const selectedEvent = window.availableEvents?.find(event => event.id == eventId);

        if (selectedEvent) {
            // Clear and populate location dropdown
            $('#appointment_location_type').html('<option value="">{{ __("Select a location") }}</option>');

            // Store the selected event data globally for location updates
            window.selectedEventData = selectedEvent;

            if (selectedEvent.locations_data) {
                // New multi-location system
                try {
                    const locations = JSON.parse(selectedEvent.locations_data);
                    if (locations && locations.length > 0) {
                        // Populate location dropdown with all available locations
                        locations.forEach(function(location, index) {
                            $('#appointment_location_type').append(
                                `<option value="${index}" data-location-value="${location.value || 'Not specified'}" data-location-display="${location.display}">
                                    ${location.display}
                                </option>`
                            );
                        });

                        // Auto-select first location and update value
                        $('#appointment_location_type').val('0');
                        $('#appointment_location_value').val(locations[0].value || 'Not specified');
                    }
                } catch (e) {
                    console.error('Error parsing locations_data:', e);
                    $('#appointment_location_type').append('<option value="error">Location data error</option>');
                    $('#appointment_location_value').val('Location data error');
                }
            } else {
                // Legacy single location system
                const location = selectedEvent.location;
                const locationDisplay = getLocationDisplayName(location);
                let locationValue = '';

                if (location === 'in_person') {
                    locationValue = selectedEvent.physical_address || 'Physical address not specified';
                } else {
                    locationValue = selectedEvent.meet_link || 'Meeting link not specified';
                }

                // Add single location option
                $('#appointment_location_type').append(
                    `<option value="0" data-location-value="${locationValue}" data-location-display="${locationDisplay}">
                        ${locationDisplay}
                    </option>`
                );

                // Auto-select and update value
                $('#appointment_location_type').val('0');
                $('#appointment_location_value').val(locationValue);
            }

            // Set the event date from the calendar event's start date
            if (selectedEvent.start_date) {
                const eventDate = new Date(selectedEvent.start_date);
                const formattedDate = eventDate.toISOString().split('T')[0]; // YYYY-MM-DD format
                $('#appointment_date').val(formattedDate);
            }
        } else {
            // Fallback to data attributes if global events not available
            const location = selectedOption.data('location');
            const locationValue = selectedOption.data('location-value');
            const locationDisplay = getLocationDisplayName(location);

            $('#appointment_location_type').html('<option value="">{{ __("Select a location") }}</option>');
            $('#appointment_location_type').append(
                `<option value="0" data-location-value="${locationValue || 'Not specified'}" data-location-display="${locationDisplay}">
                    ${locationDisplay}
                </option>`
            );
            $('#appointment_location_type').val('0');
            $('#appointment_location_value').val(locationValue || 'Not specified');

            // Set current date as fallback
            $('#appointment_date').val(new Date().toISOString().split('T')[0]);
        }

        // Generate time slots for the selected event
        generateSimpleTimeSlots();
        
        // Load custom fields for the selected event
        loadEventCustomFields(eventId);
        
        // Handle payment information
        updatePaymentInformation(selectedEvent);
    } else {
        // Clear fields if no event selected
        $('#appointment_location_type').html('<option value="">{{ __("Select a location") }}</option>');
        $('#appointment_location_value').val('');
        $('#appointment_date').val('');
        $('#appointment_timeslot').html('<option value="">{{ __("Select a time slot") }}</option>');
        $('#appointment_custom_fields_container').hide();
        $('#appointment_payment_container').hide();
        $('#appointment_custom_fields_content').empty();
        window.selectedEventData = null;
    }
}

function updateLocationValue() {
    const selectedOption = $('#appointment_location_type option:selected');
    const locationValue = selectedOption.data('location-value');

    if (locationValue) {
        $('#appointment_location_value').val(locationValue);
    } else {
        $('#appointment_location_value').val('');
    }
}

function getLocationDisplayName(location) {
    const locationNames = {
        'in_person': 'In Person',
        'zoom': 'Zoom',
        'skype': 'Skype',
        'meet': 'Google Meet',
        'phone': 'Phone Call',
        'others': 'Other'
    };
    return locationNames[location] || location;
}

function generateTimeSlots(eventId, duration) {
    console.log('Generating time slots for event:', eventId);

    // Generate time slots based on booking table data
    generateTimeSlotsFromBookingData(eventId);
}

function generateSimpleTimeSlots() {
    console.log('Generating simple time slots');
    $('#appointment_timeslot').html('<option value="">{{ __("Select a time slot") }}</option>');

    // Start from 10:00 AM and go to 5:00 PM in 30-minute intervals
    let currentHour = 10;
    let currentMinute = 0;
    const endHour = 17;

    while (currentHour < endHour || (currentHour === endHour && currentMinute === 0)) {
        const timeString = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
        const displayTime = formatTime12Hour(timeString);

        console.log('Adding simple time slot:', timeString, '->', displayTime);
        $('#appointment_timeslot').append(`<option value="${timeString}">${displayTime}</option>`);

        // Add 30 minutes
        currentMinute += 30;
        if (currentMinute >= 60) {
            currentMinute = 0;
            currentHour += 1;
        }
    }

    console.log('Simple time slots generation completed');
}

function generateTimeSlotsFromBookingData(eventId) {
    console.log('Getting booking data for event:', eventId);

    // Get booking time from the booking table for this event
    $.ajax({
        url: '{{ route("calendar-events.index") }}',
        method: 'GET',
        success: function(response) {
            console.log('Events response:', response);
            $('#appointment_timeslot').html('<option value="">{{ __("Select a time slot") }}</option>');

            if (response.success && response.data) {
                // Find the selected event and get its booking time
                const selectedEvent = response.data.find(event => event.id == eventId);
                console.log('Selected event:', selectedEvent);

                let bookingStartTime = '10:00'; // Default start time

                if (selectedEvent && selectedEvent.bookings && selectedEvent.bookings.length > 0) {
                    // Get the time from the first booking for this event
                    bookingStartTime = selectedEvent.bookings[0].time;
                    console.log('Found booking start time:', bookingStartTime);
                } else {
                    console.log('No bookings found, using default time:', bookingStartTime);
                }

                // Parse start time from booking
                const [startHour, startMinute] = bookingStartTime.split(':').map(Number);
                console.log('Start hour:', startHour, 'Start minute:', startMinute);

                // End time is 5:00 PM (17:00)
                const endHour = 17;
                const endMinute = 0;

                // Generate time slots in 30-minute intervals
                let currentHour = startHour;
                let currentMinute = startMinute;

                console.log('Generating time slots from', currentHour + ':' + currentMinute, 'to 17:00');

                while (currentHour < endHour || (currentHour === endHour && currentMinute <= endMinute)) {
                    const timeString = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
                    const displayTime = formatTime12Hour(timeString);

                    console.log('Adding time slot:', timeString, '->', displayTime);
                    $('#appointment_timeslot').append(`<option value="${timeString}">${displayTime}</option>`);

                    // Add 30 minutes
                    currentMinute += 30;
                    if (currentMinute >= 60) {
                        currentMinute = 0;
                        currentHour += 1;
                    }

                    // Stop if we've reached past 5:00 PM
                    if (currentHour > endHour || (currentHour === endHour && currentMinute > endMinute)) {
                        break;
                    }
                }

                console.log('Time slots generation completed');
            } else {
                console.log('No event data found, using fallback');
                // Final fallback
                generateFallbackTimeSlots();
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading booking data:', error);
            // Final fallback
            generateFallbackTimeSlots();
        }
    });
}

function generateDefaultTimeSlots(duration = 60) {
    $('#appointment_timeslot').html('<option value="">{{ __("Select a time slot") }}</option>');

    const selectedEventId = $('#appointment_calendar_event').val();

    if (!selectedEventId) {
        return;
    }

    // Get booking time from the booking table for this event
    $.ajax({
        url: '{{ route("calendar-events.index") }}',
        method: 'GET',
        success: function(response) {
            if (response.success && response.data) {
                // Find the selected event and get its booking time
                const selectedEvent = response.data.find(event => event.id == selectedEventId);
                let bookingStartTime = '09:00'; // Default start time

                if (selectedEvent && selectedEvent.bookings && selectedEvent.bookings.length > 0) {
                    // Get the time from the first booking (or you can modify this logic)
                    bookingStartTime = selectedEvent.bookings[0].time;
                }

                // Parse start time from booking
                const [startHour, startMinute] = bookingStartTime.split(':').map(Number);

                // End time is 5:00 PM (17:00)
                const endHour = 17;
                const endMinute = 0;

                // Generate time slots in 30-minute intervals
                let currentHour = startHour;
                let currentMinute = startMinute;

                while (currentHour < endHour || (currentHour === endHour && currentMinute <= endMinute)) {
                    const timeString = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
                    const displayTime = formatTime12Hour(timeString);

                    $('#appointment_timeslot').append(`<option value="${timeString}">${displayTime}</option>`);

                    // Add 30 minutes
                    currentMinute += 30;
                    if (currentMinute >= 60) {
                        currentMinute = 0;
                        currentHour += 1;
                    }

                    // Stop if we've reached past 5:00 PM
                    if (currentHour > endHour || (currentHour === endHour && currentMinute > endMinute)) {
                        break;
                    }
                }
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading booking times:', error);
            // Fallback to default 9 AM to 5 PM slots
            generateFallbackTimeSlots();
        }
    });
}

function generateFallbackTimeSlots() {
    console.log('Generating fallback time slots');
    $('#appointment_timeslot').html('<option value="">{{ __("Select a time slot") }}</option>');

    // Generate default time slots from 10 AM to 5 PM in 30-minute intervals
    let currentHour = 10;
    let currentMinute = 0;
    const endHour = 17;
    const endMinute = 0;

    console.log('Fallback: generating from 10:00 to 17:00');

    while (currentHour < endHour || (currentHour === endHour && currentMinute <= endMinute)) {
        const timeString = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
        const displayTime = formatTime12Hour(timeString);

        console.log('Fallback: adding time slot:', timeString, '->', displayTime);
        $('#appointment_timeslot').append(`<option value="${timeString}">${displayTime}</option>`);

        // Add 30 minutes
        currentMinute += 30;
        if (currentMinute >= 60) {
            currentMinute = 0;
            currentHour += 1;
        }

        // Stop if we've reached past 5:00 PM
        if (currentHour > endHour || (currentHour === endHour && currentMinute > endMinute)) {
            break;
        }
    }

    console.log('Fallback time slots generation completed');
}

function formatTime12Hour(time24) {
    const [hours, minutes] = time24.split(':');
    const hour12 = hours % 12 || 12;
    const ampm = hours < 12 ? 'AM' : 'PM';
    return `${hour12}:${minutes} ${ampm}`;
}

// Populate timezone dropdown with GMT as default
function populateTimezones() {
    const timezoneSelect = $('#appointment_timezone');

    // Clear existing options except the first one
    timezoneSelect.find('option:not(:first)').remove();

    // Add GMT as the primary option (selected by default)
    timezoneSelect.append(`<option value="GMT" selected>GMT (Greenwich Mean Time) (UTC+00:00)</option>`);

    // Add separator
    timezoneSelect.append('<option disabled>──────────────────────</option>');

    // Add common timezones
    const commonTimezones = [
        { value: 'UTC', display: 'UTC (Coordinated Universal Time) (UTC+00:00)' },
        { value: 'Europe/London', display: 'London (UTC+00:00/+01:00)' },
        { value: 'America/New_York', display: 'New York (UTC-05:00/-04:00)' },
        { value: 'America/Los_Angeles', display: 'Los Angeles (UTC-08:00/-07:00)' },
        { value: 'Europe/Paris', display: 'Paris (UTC+01:00/+02:00)' },
        { value: 'Asia/Tokyo', display: 'Tokyo (UTC+09:00)' },
        { value: 'Asia/Dubai', display: 'Dubai (UTC+04:00)' },
        { value: 'Asia/Kolkata', display: 'India (UTC+05:30)' },
        { value: 'Australia/Sydney', display: 'Sydney (UTC+10:00/+11:00)' }
    ];

    // Add common timezones group
    timezoneSelect.append('<optgroup label="Common Timezones">');
    commonTimezones.forEach(tz => {
        timezoneSelect.append(`<option value="${tz.value}">${tz.display}</option>`);
    });
    timezoneSelect.append('</optgroup>');

    // Add all other timezones
    const timezones = Intl.supportedValuesOf('timeZone');
    const groupedTimezones = {};

    timezones.forEach(timezone => {
        // Skip if already in common timezones
        if (commonTimezones.some(ct => ct.value === timezone) || timezone === 'GMT') return;

        const parts = timezone.split('/');
        const region = parts[0];
        const city = parts.slice(1).join('/');

        if (!groupedTimezones[region]) {
            groupedTimezones[region] = [];
        }

        const offset = getTimezoneOffset(timezone);
        // Format timezone display name
        let displayName = city.replace(/_/g, ' ');

        // Special formatting for common locations
        if (timezone === 'Asia/Kolkata') {
            displayName = 'India';
        } else if (timezone === 'Asia/Shanghai') {
            displayName = 'China';
        } else if (timezone === 'Europe/London') {
            displayName = 'United Kingdom';
        }

        groupedTimezones[region].push({
            value: timezone,
            display: `${displayName} ${offset}`,
            offset: offset
        });
    });

    // Sort regions and add to select
    Object.keys(groupedTimezones).sort().forEach(region => {
        // Sort cities within region by offset
        groupedTimezones[region].sort((a, b) => {
            const offsetA = parseFloat(a.offset.replace(/[^\d.-]/g, ''));
            const offsetB = parseFloat(b.offset.replace(/[^\d.-]/g, ''));
            return offsetA - offsetB;
        });

        // Add region header
        timezoneSelect.append(`<optgroup label="${region}">`);

        // Add cities in this region
        groupedTimezones[region].forEach(tz => {
            timezoneSelect.append(`<option value="${tz.value}">${tz.display}</option>`);
        });

        timezoneSelect.append('</optgroup>');
    });
}

// Helper function to get timezone offset
function getTimezoneOffset(timezone) {
    const now = new Date();
    const utc = new Date(now.getTime() + (now.getTimezoneOffset() * 60000));
    const targetTime = new Date(utc.toLocaleString("en-US", {timeZone: timezone}));
    const diff = targetTime.getTime() - utc.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    const sign = hours >= 0 ? '+' : '-';
    const absHours = Math.abs(hours);
    const absMinutes = Math.abs(minutes);

    return `(UTC${sign}${absHours.toString().padStart(2, '0')}:${absMinutes.toString().padStart(2, '0')})`;
}

// Function to update payment information in appointment form
function updatePaymentInformation(event) {
    if (event.payment_required && event.payment_amount > 0) {
        // Show payment container
        $('#appointment_payment_container').show();
        
        // Set total amount
        $('#appointment_total_amount').val(parseFloat(event.payment_amount).toFixed(2));
        
        // Set currency symbols
        const currency = event.payment_currency || 'USD';
        const currencySymbol = getCurrencySymbol(currency);
        $('#payment_currency_symbol, #partial_payment_currency_symbol, #remaining_payment_currency_symbol').text(currencySymbol);
        
        // Check if partial payment is enabled
        if (event.partial_payment_enabled && event.partial_payment_percentage) {
            $('#partial_payment_option_container').show();
            
            // Calculate partial payment amounts
            const totalAmount = parseFloat(event.payment_amount);
            const partialPercentage = parseFloat(event.partial_payment_percentage);
            const partialAmount = (totalAmount * partialPercentage) / 100;
            const remainingAmount = totalAmount - partialAmount;
            
            $('#appointment_partial_amount').val(partialAmount.toFixed(2));
            $('#appointment_remaining_amount').val(remainingAmount.toFixed(2));
            $('#partial_payment_percentage_text').text(`${partialPercentage}% of total amount`);
        } else {
            $('#partial_payment_option_container').hide();
        }
    } else {
        // Hide payment container if no payment required
        $('#appointment_payment_container').hide();
    }
}

// Function to get currency symbol
function getCurrencySymbol(currency) {
    const symbols = {
        'USD': '$',
        'EUR': '€',
        'GBP': '£',
        'INR': '₹',
        'CAD': 'C$',
        'AUD': 'A$'
    };
    return symbols[currency] || '$';
}

// Handle appointment booking form submission
$(document).ready(function() {
    // Load CRM contacts when document is ready
    loadCrmContacts();
    
    // Payment type change event listeners
    $('input[name="payment_type"]').on('change', function() {
        const paymentType = $(this).val();
        if (paymentType === 'partial') {
            $('#partial_payment_details').show();
        } else {
            $('#partial_payment_details').hide();
        }
    });

    $('#appointmentBookingForm').on('submit', function(e) {
        e.preventDefault();

        // Collect form data
        const formData = new FormData(this);
        
        // Add contact details from selected contact
        if (window.selectedContact) {
            if (window.selectedContact.email) {
                formData.append('email', window.selectedContact.email);
            }
            if (window.selectedContact.phone) {
                formData.append('phone', window.selectedContact.phone);
            }
        }
        
        // Add custom fields data if they exist
        const customFields = {};
        const customFieldsValue = {};
        
        $('#appointment_custom_fields_content input, #appointment_custom_fields_content select, #appointment_custom_fields_content textarea').each(function() {
            const fieldName = $(this).attr('name');
            const fieldValue = $(this).val();
            
            if (fieldName && fieldValue) {
                if (fieldName.startsWith('custom_fields[')) {
                    const fieldId = fieldName.match(/\[(.*?)\]/)[1];
                    customFields[fieldId] = fieldName;
                    customFieldsValue[fieldId] = fieldValue;
                }
            }
        });

        // Add custom fields to form data
        if (Object.keys(customFields).length > 0) {
            formData.append('custom_fields', JSON.stringify(customFields));
            formData.append('custom_fields_value', JSON.stringify(customFieldsValue));
        }

        // Validate required fields
        const requiredFields = ['name', 'event_id', 'date', 'time'];
        for (let field of requiredFields) {
            if (!formData.get(field)) {
                alert('Please fill in all required fields');
                return;
            }
        }

        // Handle payment information
        if (window.selectedEventData && window.selectedEventData.payment_required && window.selectedEventData.payment_amount > 0) {
            const paymentType = $('input[name="payment_type"]:checked').val();
            const totalAmount = parseFloat(window.selectedEventData.payment_amount);
            
            if (paymentType === 'partial') {
                // Add partial payment information
                const partialPercentage = parseFloat(window.selectedEventData.partial_payment_percentage);
                const partialAmount = (totalAmount * partialPercentage) / 100;
                
                formData.append('payment_amount', partialAmount);
                formData.append('payment_type', 'partial');
                formData.append('partial_payment_percentage', partialPercentage);
                formData.append('remaining_amount', totalAmount - partialAmount);
            } else {
                // Full payment
                formData.append('payment_amount', totalAmount);
                formData.append('payment_type', 'full');
            }
            
            formData.append('payment_currency', window.selectedEventData.payment_currency || 'USD');
        }

        // Disable submit button
        $('#appointmentSubmitBtn').prop('disabled', true).html('<i class="ti ti-loader me-1"></i>{{ __("Booking...") }}');

        // Submit appointment booking using public bookings route
        $.ajax({
            url: '{{ route("public-bookings.store") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    show_toastr('success', 'Appointment booked successfully!');
                    $('#appointmentBookingModal').modal('hide');
                    resetAppointmentForm();

                    // Refresh calendar to show the new appointment
                    if (typeof calendar !== 'undefined') {
                        calendar.refetchEvents();
                    }
                } else {
                    show_toastr('error', response.message || 'Failed to book appointment');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error booking appointment:', error);
                let errorMessage = 'Error booking appointment';

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                }

                show_toastr('error', errorMessage);
            },
            complete: function() {
                // Re-enable submit button
                $('#appointmentSubmitBtn').prop('disabled', false).html('<i class="ti ti-check me-1"></i>{{ __("Book Appointment") }}');
            }
        });
    });
});

function resetAppointmentForm() {
    $('#appointmentBookingForm')[0].reset();
    $('#appointment_calendar_event').html('<option value="">{{ __("Select an event") }}</option>');
    $('#appointment_timeslot').html('<option value="">{{ __("Select a time slot") }}</option>');
    $('#appointment_location_type').html('<option value="">{{ __("Select a location") }}</option>');
    $('#appointment_location_value').val('');
    $('#appointment_date').val('');
    $('#appointment_custom_fields_container').hide();
    $('#appointment_custom_fields_content').empty();
    $('#appointment_payment_container').hide();
    $('#partial_payment_details').hide();
    // Remove visual feedback
    $('#appointment_contact_name').removeClass('contact-selected');
    // Clear global event data
    window.selectedEventData = null;
    window.selectedContact = null;
}

// Load CRM contacts for appointment booking
function loadCrmContacts() {
    $.ajax({
        url: '{{ route("appointment-contacts") }}',
        method: 'GET',
        success: function(response) {
            if (response.success && response.contacts) {
                // Populate the select dropdown for contact selection
                const select = $('#appointment_contact_name');
                select.empty();
                select.append('<option value="">{{ __("Select a contact from CRM") }}</option>');
                
                response.contacts.forEach(contact => {
                    const displayText = `${contact.name} (${contact.type})`;
                    select.append(`<option value="${contact.name}" data-email="${contact.email}" data-phone="${contact.phone}">${displayText}</option>`);
                });
                
                // Store contacts globally for lookup
                window.crmContacts = response.contacts;
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading CRM contacts:', error);
        }
    });
}

// Update contact details when a contact is selected
function updateContactDetails() {
    const contactName = $('#appointment_contact_name').val();
    const contactSelect = $('#appointment_contact_name');
    
    if (window.crmContacts && contactName) {
        const contact = window.crmContacts.find(c => c.name === contactName);
        
        if (contact) {
            // Store contact details for form submission
            window.selectedContact = contact;
            // Add visual feedback
            contactSelect.addClass('contact-selected');
        }
    } else {
        window.selectedContact = null;
        // Remove visual feedback
        contactSelect.removeClass('contact-selected');
    }
}

// Load custom fields for the selected event
function loadEventCustomFields(eventId) {
    if (!eventId) {
        $('#appointment_custom_fields_container').hide();
        return;
    }
    
    // Find the selected event from available events
    const selectedEvent = window.availableEvents ? window.availableEvents.find(event => event.id == eventId) : null;
    
    if (selectedEvent && selectedEvent.custom_fields && selectedEvent.custom_fields.length > 0) {
        const container = $('#appointment_custom_fields_content');
        container.empty();
        
        selectedEvent.custom_fields.forEach((field, index) => {
            const fieldHtml = createCustomFieldHtml(field, index);
            container.append(fieldHtml);
        });
        
        $('#appointment_custom_fields_container').show();
    } else {
        $('#appointment_custom_fields_container').hide();
    }
}

// Create HTML for custom field (optional)
function createCustomFieldHtml(field, index) {
    const fieldId = field.id || index;
    const fieldName = field.name || 'Custom Field';
    const fieldType = field.type || 'text';
    const placeholder = field.placeholder || `Enter ${fieldName.toLowerCase()}`;
    const isRequired = field.required || false;

    let inputHtml = '';

    switch(fieldType) {
        case 'textarea':
            inputHtml = `<textarea class="form-control" name="custom_fields[${fieldId}]" placeholder="${placeholder}" ${isRequired ? 'required' : ''}></textarea>`;
            break;
        case 'select':
            const selectOptions = field.options || [];
            inputHtml = `<select class="form-control" name="custom_fields[${fieldId}]" ${isRequired ? 'required' : ''}>
                <option value="">${placeholder}</option>
                ${selectOptions.map(option => `<option value="${option}">${option}</option>`).join('')}
            </select>`;
            break;
        case 'radio':
            const radioOptions = field.options || [];
            if (radioOptions.length > 0) {
                inputHtml = radioOptions.map((option, optionIndex) => `
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="custom_fields[${fieldId}]"
                               id="custom_field_${fieldId}_${optionIndex}" value="${option}" ${isRequired ? 'required' : ''}>
                        <label class="form-check-label" for="custom_field_${fieldId}_${optionIndex}">
                            ${option}
                        </label>
                    </div>
                `).join('');
            } else {
                inputHtml = `<p class="text-muted">No options available</p>`;
            }
            break;
        case 'checkbox':
            const checkboxOptions = field.options || [];
            if (checkboxOptions.length > 0) {
                inputHtml = checkboxOptions.map((option, optionIndex) => `
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="custom_fields[${fieldId}][]"
                               id="custom_field_${fieldId}_${optionIndex}" value="${option}">
                        <label class="form-check-label" for="custom_field_${fieldId}_${optionIndex}">
                            ${option}
                        </label>
                    </div>
                `).join('');
            } else {
                inputHtml = `<p class="text-muted">No options available</p>`;
            }
            break;
        case 'date':
            inputHtml = `<input type="date" class="form-control" name="custom_fields[${fieldId}]" ${isRequired ? 'required' : ''}>`;
            break;
        default:
            inputHtml = `<input type="text" class="form-control" name="custom_fields[${fieldId}]" placeholder="${placeholder}" ${isRequired ? 'required' : ''}>`;
    }

    return `
        <div class="mb-3">
            <label class="form-label">${fieldName}${isRequired ? ' <span class="text-danger">*</span>' : ''}</label>
            ${inputHtml}
        </div>
    `;
}
    // Update your editEvent function to populate custom fields if they exist:
    // (This function is removed to avoid duplication - see the main editEvent function below)
// Add this to your global functions at the bottom:
window.toggleCustomField = toggleCustomField;//custom field

// Global variable to store selected locations
let selectedLocations = [];

// Open location modal
function openLocationModal(locationType) {
    const modalId = locationType + 'LocationModal';
    const modal = new bootstrap.Modal(document.getElementById(modalId));
    modal.show();
}
// Save location from modal
function saveLocation(locationType) {
    let locationData = {
        type: locationType,
        value: '',
        display: ''
    };
    // Get the value based on location type
    switch(locationType) {
        case 'zoom':
            locationData.value = $('#zoom_link').val();
            locationData.display = 'Zoom';
            break;
        case 'in_person':
            locationData.value = $('#in_person_address').val();
            locationData.display = 'In-person meeting';
            break;
        case 'phone':
            locationData.value = $('#phone_number').val();
            locationData.display = 'Phone call';
            break;
        case 'meet':
            locationData.value = $('#meet_link').val();
            locationData.display = 'Google Meet';
            break;
        case 'skype':
            locationData.value = $('#skype_link').val();
            locationData.display = 'Skype';
            break;
        case 'others':
            locationData.value = $('#others_details').val();
            locationData.display = 'Others';
            break;
    }

    // Validate that value is provided
    if (!locationData.value.trim()) {
        alert('Please enter the location details');
        return;
    }

    // Check if this location type already exists
    const existingIndex = selectedLocations.findIndex(loc => loc.type === locationType);
    if (existingIndex !== -1) {
        // Update existing location
        selectedLocations[existingIndex] = locationData;
    } else {
        // Add new location
        selectedLocations.push(locationData);
    }

    // Update the display
    updateSelectedLocationsDisplay();

    // Close the modal
    const modalId = locationType + 'LocationModal';
    const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
    modal.hide();

    // Clear the form
    clearLocationModal(locationType);
}

// Clear location modal form
function clearLocationModal(locationType) {
    switch(locationType) {
        case 'zoom':
            $('#zoom_link').val('');
            break;
        case 'in_person':
            $('#in_person_address').val('');
            break;
        case 'phone':
            $('#phone_number').val('');
            break;
        case 'meet':
            $('#meet_link').val('');
            break;
        case 'skype':
            $('#skype_link').val('');
            break;
        case 'others':
            $('#others_details').val('');
            break;
    }
}

// Remove location
function removeLocation(locationType) {
    selectedLocations = selectedLocations.filter(loc => loc.type !== locationType);
    updateSelectedLocationsDisplay();
}

// Update the display of selected locations
function updateSelectedLocationsDisplay() {
    const container = $('#selected-locations-container');
    container.empty();

    if (selectedLocations.length === 0) {
        container.hide();
        $('#event_locations_data').val('');
        return;
    }

    container.show();

    selectedLocations.forEach(location => {
        const chip = createLocationChip(location);
        container.append(chip);
    });

    // Update hidden input with location data
    $('#event_locations_data').val(JSON.stringify(selectedLocations));
}

// Create location chip element
function createLocationChip(location) {
    const iconMap = {
        'zoom': 'ti-video text-primary',
        'in_person': 'ti-map-pin text-success',
        'phone': 'ti-phone text-info',
        'meet': 'ti-brand-google text-warning',
        'skype': 'ti-brand-skype text-primary',
        'others': 'ti-dots text-secondary'
    };

    const icon = iconMap[location.type] || 'ti-map-pin';

    return $(`
        <div class="d-inline-flex align-items-center bg-light border rounded-pill px-3 py-2 me-2 mb-2">
            <i class="ti ${icon} me-2"></i>
            <span class="fw-medium">${location.display}</span>
            <button type="button" class="btn btn-sm btn-link text-danger p-0 ms-2" onclick="removeLocation('${location.type}')" title="Remove">
                <i class="ti ti-x"></i>
            </button>
        </div>
    `);
}

// Legacy function for backward compatibility
function toggleLocationFields() {
    // This function is kept for backward compatibility but the new system doesn't use it
    console.log('toggleLocationFields called - using new location system');
}

// Reset form to initial state
function resetForm() {
    $('#createEventForm')[0].reset();

    // Clear selected locations
    selectedLocations = [];
    updateSelectedLocationsDisplay();

    // Clear all location modal forms
    clearLocationModal('zoom');
    clearLocationModal('in_person');
    clearLocationModal('phone');
    clearLocationModal('meet');
    clearLocationModal('skype');
    clearLocationModal('others');

    // Clear custom fields container
    $('#custom-fields-container').empty();
    customFieldCounter = 0;

    // Reset minimum notice to default values
    $('#minimum_notice_value').val(0);
    $('#minimum_notice_unit').val('minutes');
    $('#minimum_notice').val(0);
    updateMinimumNoticePreview(0, 'minutes');

    // Reset status to default value
    $('#event_status').val('active');

    // Reset date range fields to default (indefinitely)
    $('#indefinitely_option').prop('checked', true);
    $('#calendar_days_input').val(30);
    $('#date_range_start_input').val('');
    $('#date_range_end_input').val('');
    handleDateRangeChange(); // Update field states

    // Reset date override fields
    $('#override_date').val('');
    $('#override_time').val('');
    $('#unavailable-slots-list').empty();

    // Reset payment fields
    $('#payment_required').prop('checked', false);
    $('#payment_amount').prop('disabled', true).val('');
    $('#payment_currency').prop('disabled', true).val('USD');
    $('#partial_payment_settings').hide();
    $('#partial_payment_enabled').prop('checked', false);
    $('#partial_payment_percentage').prop('disabled', true).val('');

    // Reset modal title and button text
    $('.modal-title').text('{{ __("Create New Event") }}');
    $('#submitBtn').text('{{ __("Create Event") }}');

    // Reset Summernote editor
    $('#event_description').summernote('code', '');

    // Reset editing state
    editingEventId = null;

    console.log('Form reset completed');
}

// Load staff data for dropdown
function loadStaffData() {
    $.ajax({
        url: '{{ route("calendar-events.staff-data") }}',
        type: 'GET',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            console.log('Staff data response:', response);
            if (response.success) {
                const staffSelect = $('#assigned_staff_id');
                staffSelect.empty();
                staffSelect.append('<option value="">{{ __("Select Staff (Optional)") }}</option>');

                if (response.data && response.data.length > 0) {
                    response.data.forEach(function(staff) {
                        const typeLabel = staff.type.charAt(0).toUpperCase() + staff.type.slice(1);
                        staffSelect.append(`<option value="${staff.id}">${staff.name} (${typeLabel})</option>`);
                    });
                    console.log(`Loaded ${response.data.length} staff members`);
                } else {
                    console.log('No staff data found');
                }
            } else {
                console.error('Failed to load staff data:', response.message);
            }
        },
        error: function(xhr) {
            console.error('Error loading staff data:', xhr);
        }
    });
}

// Open create event modal
function openCreateEventModal() {
    console.log('Opening create modal...');
    resetForm();
    loadStaffData();
    loadBookingCustomFields();
    $('#createEventModal').modal('show');
}

// Edit event function
function editEvent(eventId) {
    console.log('Editing event:', eventId);
    
    // Set editing state
    editingEventId = eventId;
    
    // Update modal title and button text
    $('.modal-title').text('{{ __("Event Details") }}');
    $('#submitBtn').text('{{ __("Update Event") }}');
    
    // Load event data
    const url = `{{ url('calendar-events') }}/${eventId}/edit-data`;
    console.log('Making AJAX request to:', url);

    $.ajax({
        url: url,
        method: 'GET',
        success: function(response) {
            console.log('Event data loaded:', response);
            
            if (response.success) {
                const event = response.data;
                
                // Populate form fields
                $('#event_title').val(event.title || '');
                $('#event_duration').val(event.duration || 60);
                $('#booking_per_slot').val(event.booking_per_slot || 1);

                // Set minimum notice using the new format
                setMinimumNoticeFromMinutes(event.minimum_notice || 0);

                $('#event_description').summernote('code', event.description || '');
                $('#custom_redirect_url').val(event.custom_redirect_url || '');

                // Set status field
                $('#event_status').val(event.status || 'active');

                // Handle location data - check if new format exists, otherwise use legacy format
                selectedLocations = [];

                if (event.locations_data) {
                    // New format - parse JSON data
                    try {
                        selectedLocations = JSON.parse(event.locations_data);
                    } catch (e) {
                        console.error('Error parsing locations_data:', e);
                        selectedLocations = [];
                    }
                } else if (event.location) {
                    // Legacy format - convert to new format
                    const legacyLocation = {
                        type: event.location,
                        display: getLocationDisplayName(event.location),
                        value: ''
                    };

                    // Set the value based on location type
                    if (event.location === 'in_person') {
                        legacyLocation.value = event.physical_address || '';
                    } else if (['zoom', 'skype', 'meet', 'others'].includes(event.location)) {
                        legacyLocation.value = event.meet_link || '';
                    }

                    if (legacyLocation.value) {
                        selectedLocations = [legacyLocation];
                    }
                }

                // Update the location display
                updateSelectedLocationsDisplay();

                // Populate custom fields if they exist
                populateCustomFieldsForEdit(event);

                // Populate date range fields
                if (event.date_range_type) {
                    $(`input[name="date_range_type"][value="${event.date_range_type}"]`).prop('checked', true);

                    if (event.date_range_type === 'calendar_days' && event.date_range_days) {
                        $('#calendar_days_input').val(event.date_range_days);
                    } else if (event.date_range_type === 'date_range') {
                        if (event.date_range_start) {
                            $('#date_range_start_input').val(event.date_range_start);
                        }
                        if (event.date_range_end) {
                            $('#date_range_end_input').val(event.date_range_end);
                        }
                    }
                } else {
                    // Default to indefinitely for backward compatibility
                    $('#indefinitely_option').prop('checked', true);
                }

                // Trigger date range change to enable/disable appropriate fields
                handleDateRangeChange();

                // Populate date overrides if they exist
                $('#unavailable-slots-list').empty(); // Clear existing overrides
                if (event.date_override) {
                    let overrides = [];
                    if (typeof event.date_override === 'string') {
                        try {
                            overrides = JSON.parse(event.date_override);
                        } catch (e) {
                            console.error('Error parsing date_override:', e);
                        }
                    } else if (Array.isArray(event.date_override)) {
                        overrides = event.date_override;
                    }

                    overrides.forEach(function(override) {
                        if (typeof override === 'string') {
                            populateDateOverride(override);
                        } else if (override.datetime) {
                            populateDateOverride(override.datetime);
                        }
                    });
                }

                // Populate weekly availability data
                populateWeeklyAvailability(event.weekly_availability);

                // Populate payment fields
                if (event.payment_required) {
                    $('#payment_required').prop('checked', true);
                    $('#payment_amount').prop('disabled', false).val(event.payment_amount || '');
                    $('#payment_currency').prop('disabled', false).val(event.payment_currency || 'USD');
                    
                    // Show partial payment settings if payment is required
                    $('#partial_payment_settings').show();
                    
                    if (event.partial_payment_enabled) {
                        $('#partial_payment_enabled').prop('checked', true);
                        $('#partial_payment_percentage').prop('disabled', false).val(event.partial_payment_percentage || '');
                    } else {
                        $('#partial_payment_enabled').prop('checked', false);
                        $('#partial_payment_percentage').prop('disabled', true).val('');
                    }
                } else {
                    $('#payment_required').prop('checked', false);
                    $('#payment_amount').prop('disabled', true).val('');
                    $('#payment_currency').prop('disabled', true).val('USD');
                    $('#partial_payment_settings').hide();
                    $('#partial_payment_enabled').prop('checked', false);
                    $('#partial_payment_percentage').prop('disabled', true).val('');
                }

                // Load staff data and set selected staff
                loadStaffData();
                setTimeout(function() {
                    $('#assigned_staff_id').val(event.assigned_staff_id || '');
                }, 100);

                // Show modal
                $('#createEventModal').modal('show');
            } else {
                show_toastr('error', response.message || 'Failed to load event');
            }
        },
        error: function(xhr) {
            console.log('Error loading event:', xhr.responseText);
            show_toastr('error', 'Error loading event details. Please try again.');
        }
    });
}



// Switch between views
function switchView(viewType) {
    console.log('switchView called with viewType:', viewType);

    try {
        // Remove active classes and set all buttons to inactive state
        $('#calendar-btn, #events-btn, #appointment-btn, #bookings-btn').each(function() {
            console.log('Resetting button:', this.id);
            $(this).removeClass('active btn-primary bg-primary').addClass('btn-outline-primary');
        });

        // Add active class and styling to the clicked button
        let activeBtn;
        if(viewType === 'calendar') {
            activeBtn = $('#calendar-btn');
        } else if(viewType === 'events') {
            activeBtn = $('#events-btn');
        } else if(viewType === 'appointment') {
            activeBtn = $('#appointment-btn');
        } else if(viewType === 'bookings') {
            activeBtn = $('#bookings-btn');
        }

        if(activeBtn && activeBtn.length) {
            console.log('Setting active button:', activeBtn.attr('id'));
            activeBtn.removeClass('btn-outline-primary').addClass('active btn-primary bg-primary');
        } else {
            console.error('Active button not found for viewType:', viewType);
        }
    } catch(error) {
        console.error('Error in switchView:', error);
    }

    // Show/hide sections
    $('#calendar-section, #events-section, #appointment-section, #bookings-section').hide();
    $('#' + viewType + '-section').show();
    
    // Handle bookings section column visibility
    if (viewType === 'bookings') {
        // Apply column visibility based on current filter
        applyColumnVisibility();
    }
    
    // Show/hide create button
    const createBtn = $('#create-event-btn');
    if (viewType === 'events') {
        createBtn.removeClass('d-none');
        loadEventsList(currentPage, currentPerPage);
    } else {
        createBtn.addClass('d-none');
    }
    // Refresh calendar if switching to calendar view
    if (viewType === 'calendar' && calendar) {
        calendar.refetchEvents();
    }
}
// Global variables for pagination
let currentPage = 1;
let currentPerPage = 10;

// Load events list with pagination
function loadEventsList(page = 1, perPage = null) {
    console.log('Loading events list...', { page, perPage });

    // Use provided perPage or current setting
    if (perPage !== null) {
        currentPerPage = perPage;
    }
    currentPage = page;

    $.ajax({
        url: '{{ route("calendar-events.index") }}',
        method: 'GET',
        data: {
            page: currentPage,
            per_page: currentPerPage
        },
        success: function(response) {
            console.log('Events loaded:', response);
            
            const tbody = $('#events-table tbody');
            tbody.empty();
            
            if (response.success && response.data && response.data.length > 0) {
                response.data.forEach(function(event) {
                    // Add null check for event and required properties
                    if (!event || !event.id || !event.start_date || !event.end_date) {
                        console.warn('Skipping invalid event in events table:', event);
                        return;
                    }

                    // Format date range based on date_range_type
                    let dateRange = '-';
                    if (event.date_range_type) {
                        switch (event.date_range_type) {
                            case 'calendar_days':
                                dateRange = `<span class="badge bg-info">${event.date_range_days || 30} calendar days</span>`;
                                break;
                            case 'date_range':
                                if (event.date_range_start && event.date_range_end) {
                                    const startDate = new Date(event.date_range_start).toLocaleDateString();
                                    const endDate = new Date(event.date_range_end).toLocaleDateString();
                                    dateRange = `<span class="badge bg-primary">${startDate} - ${endDate}</span>`;
                                } else {
                                    dateRange = '<span class="badge bg-warning">Date range not set</span>';
                                }
                                break;
                            case 'indefinitely':
                                dateRange = '<span class="badge bg-success">Indefinitely</span>';
                                break;
                            default:
                                // Fallback to old logic for backward compatibility
                                const startDate = new Date(event.start_date).toLocaleDateString();
                                const endDate = new Date(event.end_date).toLocaleDateString();
                                dateRange = startDate === endDate ? startDate : `${startDate} - ${endDate}`;
                        }
                    } else {
                        // Fallback to old logic for backward compatibility
                        const startDate = new Date(event.start_date).toLocaleDateString();
                        const endDate = new Date(event.end_date).toLocaleDateString();
                        dateRange = startDate === endDate ? startDate : `${startDate} - ${endDate}`;
                    }

                    // Format status with badge
                    const statusBadge = event.status === 'active'
                        ? '<span class="badge bg-success">Active</span>'
                        : '<span class="badge bg-secondary">Inactive</span>';

                    // Format location display with multiple location support
                    let locationDisplay = '-';
                    if (event.locations_data) {
                        try {
                            const locations = JSON.parse(event.locations_data);
                            if (locations && locations.length > 0) {
                                if (locations.length === 1) {
                                    locationDisplay = locations[0].display;
                                } else {
                                    locationDisplay = `${locations[0].display} <span class="badge bg-primary location-count-badge ms-1">+${locations.length - 1}</span>`;
                                }
                            }
                        } catch (e) {
                            console.error('Error parsing locations_data:', e);
                            locationDisplay = event.location || '-';
                        }
                    } else if (event.location) {
                        locationDisplay = event.location;
                    }

                    // Format assigned staff display
                    let assignedStaffDisplay = '-';
                    if (event.assigned_staff) {
                        const staffType = event.assigned_staff.type.charAt(0).toUpperCase() + event.assigned_staff.type.slice(1);
                        assignedStaffDisplay = `
                            <span class="badge bg-primary">
                                <i class="ti ti-user me-1"></i>${event.assigned_staff.name}
                            </span>
                            <small class="d-block text-muted">${staffType}</small>
                        `;
                    } else {
                        assignedStaffDisplay = `
                            <span class="badge bg-light text-muted">
                                <i class="ti ti-user-off me-1"></i>Not Assigned
                            </span>
                        `;
                    }

                    const row = `
                        <tr data-event-id="${event.id}">
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input event-checkbox" type="checkbox" value="${event.id}" id="event-${event.id}">
                                    <label class="form-check-label" for="event-${event.id}"></label>
                                </div>
                            </td>
                            <td><strong>${event.title}</strong></td>
                            <td>${assignedStaffDisplay}</td>
                            <td>${dateRange}</td>
                            <td>${event.duration || 60} min</td>
                            <td>${locationDisplay}</td>
                            <td>${statusBadge}</td>
                            <td>
                                <div class="d-flex flex-column">
                                    <span class="badge bg-success">${event.booking_per_slot || 1} slots</span>
                                    ${event.booking_count > 0 ? `<small class="text-muted mt-1">${event.booking_count} booked</small>` : ''}
                                </div>
                            </td>
                            <td>
                                <!-- View Button -->
                                <div style="position: relative; display: inline-block; margin-right: 6px;">
                                    <button onclick="viewEvent(${event.id})"
                                        style="background: linear-gradient(to right, #0d6efd, #6610f2); border: none; padding: 12px 12px; border-radius: 8px; display: inline-flex; align-items: center; justify-content: center; color: white; transition: transform 0.2s ease, box-shadow 0.2s ease;"
                                        onmouseover="this.style.transform='scale(1.1)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'; this.nextElementSibling.style.visibility='visible';"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none'; this.nextElementSibling.style.visibility='hidden';">
                                        <i class="ti ti-eye"></i>
                                    </button>
                                    <span style="visibility: hidden; background-color: black; color: #fff; text-align: center; padding: 4px 8px; border-radius: 4px; position: absolute; top: -30px; left: 50%; transform: translateX(-50%); white-space: nowrap; font-size: 12px; pointer-events: none;">
                                        View
                                    </span>
                                </div>

                                <!-- Edit Button -->
                                <div style="position: relative; display: inline-block; margin-right: 6px;">
                                    <button onclick="editEvent(${event.id})"
                                        style="background: linear-gradient(to right, #0f5132, #198754); border: none; padding: 12px 12px; border-radius: 8px; display: inline-flex; align-items: center; justify-content: center; color: white; transition: transform 0.2s ease, box-shadow 0.2s ease;"
                                        onmouseover="this.style.transform='scale(1.1)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'; this.nextElementSibling.style.visibility='visible';"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none'; this.nextElementSibling.style.visibility='hidden';">
                                        <i class="ti ti-pencil"></i>
                                    </button>
                                    <span style="visibility: hidden; background-color: black; color: #fff; text-align: center; padding: 4px 8px; border-radius: 4px; position: absolute; top: -30px; left: 50%; transform: translateX(-50%); white-space: nowrap; font-size: 12px;">
                                        Edit
                                    </span>
                                </div>

                                <!-- Copy Link Button -->
                                <div style="position: relative; display: inline-block; margin-right: 6px;">
                                    <button onclick="copyEventLinkFromList(${event.id}, this)"
                                        style="background: linear-gradient(to right, #6f42c1, #e83e8c); border: none; padding: 12px 12px; border-radius: 8px; display: inline-flex; align-items: center; justify-content: center; color: white; transition: transform 0.2s ease, box-shadow 0.2s ease;"
                                        onmouseover="this.style.transform='scale(1.1)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'; this.nextElementSibling.style.visibility='visible';"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none'; this.nextElementSibling.style.visibility='hidden';">
                                        <i class="ti ti-copy"></i>
                                    </button>
                                    <span style="visibility: hidden; background-color: black; color: #fff; text-align: center; padding: 4px 8px; border-radius: 4px; position: absolute; top: -30px; left: 50%; transform: translateX(-50%); white-space: nowrap; font-size: 12px;">
                                        Copy Link
                                    </span>
                                </div>

                                <!-- Delete Button -->
                                <div style="position: relative; display: inline-block;">
                                    <button onclick="deleteEvent(${event.id})"
                                        style="background: white; border: 1px solid #dc3545; padding: 10px 12px; border-radius: 8px; display: inline-flex; align-items: center; justify-content: center; color: #dc3545; transition: transform 0.2s ease, box-shadow 0.2s ease;"
                                        onmouseover="this.style.transform='scale(1.1)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'; this.nextElementSibling.style.visibility='visible';"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none'; this.nextElementSibling.style.visibility='hidden';">
                                        <i class="ti ti-trash"></i>
                                    </button>
                                    <span style="visibility: hidden; background-color: black; color: #fff; text-align: center; padding: 4px 8px; border-radius: 4px; position: absolute; top: -30px; left: 50%; transform: translateX(-50%); white-space: nowrap; font-size: 12px;">
                                        Delete
                                    </span>
                                </div>
                            </td>
                        </tr>
                    `;
                    tbody.append(row);
                });
            } else {
                // Update all counters to 0
                $('#events-count').text('0 {{ __("Events") }}');
                $('#active-events-count').text('0 {{ __("Active") }}');
                $('#total-bookings-count').text('0 {{ __("Bookings") }}');
                tbody.html('<tr><td colspan="9" class="text-center">No events found</td></tr>');
            }

            // Update pagination controls
            updatePaginationControls(response.pagination);
        },
        error: function(xhr) {
            console.log('Error loading events:', xhr.responseText);
            // Update all counters to 0 on error
            $('#events-count').text('0 {{ __("Events") }}');
            $('#active-events-count').text('0 {{ __("Active") }}');
            $('#total-bookings-count').text('0 {{ __("Bookings") }}');
            $('#events-table tbody').html('<tr><td colspan="9" class="text-center text-danger">Error loading events</td></tr>');
        }
    });
}

// Update pagination controls
function updatePaginationControls(pagination) {
    if (!pagination) {
        $('#events-pagination').hide();
        return;
    }

    // Update pagination info
    const infoText = `{{ __('Showing') }} ${pagination.from || 0} {{ __('to') }} ${pagination.to || 0} {{ __('of') }} ${pagination.total} {{ __('entries') }}`;
    $('#pagination-info-text').text(infoText);

    // Generate pagination buttons
    const paginationControls = $('#pagination-controls');
    paginationControls.empty();

    // Previous button
    if (pagination.current_page > 1) {
        paginationControls.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadEventsList(${pagination.current_page - 1}); return false;">
                    <i class="ti ti-chevron-left"></i>
                </a>
            </li>
        `);
    } else {
        paginationControls.append(`
            <li class="page-item disabled">
                <span class="page-link"><i class="ti ti-chevron-left"></i></span>
            </li>
        `);
    }

    // Page numbers
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.last_page, pagination.current_page + 2);

    // First page if not in range
    if (startPage > 1) {
        paginationControls.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadEventsList(1); return false;">1</a>
            </li>
        `);
        if (startPage > 2) {
            paginationControls.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
        }
    }

    // Page range
    for (let i = startPage; i <= endPage; i++) {
        if (i === pagination.current_page) {
            paginationControls.append(`
                <li class="page-item active">
                    <span class="page-link">${i}</span>
                </li>
            `);
        } else {
            paginationControls.append(`
                <li class="page-item">
                    <a class="page-link" href="#" onclick="loadEventsList(${i}); return false;">${i}</a>
                </li>
            `);
        }
    }

    // Last page if not in range
    if (endPage < pagination.last_page) {
        if (endPage < pagination.last_page - 1) {
            paginationControls.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
        }
        paginationControls.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadEventsList(${pagination.last_page}); return false;">${pagination.last_page}</a>
            </li>
        `);
    }

    // Next button
    if (pagination.current_page < pagination.last_page) {
        paginationControls.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadEventsList(${pagination.current_page + 1}); return false;">
                    <i class="ti ti-chevron-right"></i>
                </a>
            </li>
        `);
    } else {
        paginationControls.append(`
            <li class="page-item disabled">
                <span class="page-link"><i class="ti ti-chevron-right"></i></span>
            </li>
        `);
    }

    // Show pagination controls if there's more than one page
    if (pagination.last_page > 1) {
        $('#events-pagination').show();
    } else {
        $('#events-pagination').hide();
    }
}

// Bulk operations helper functions
function getSelectedEventIds() {
    const selectedIds = [];
    $('.event-checkbox:checked').each(function() {
        selectedIds.push(parseInt($(this).val()));
    });
    return selectedIds;
}

function updateSelectAllState() {
    const totalCheckboxes = $('.event-checkbox').length;
    const checkedCheckboxes = $('.event-checkbox:checked').length;

    if (checkedCheckboxes === 0) {
        $('#select-all-events').prop('indeterminate', false).prop('checked', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
        $('#select-all-events').prop('indeterminate', false).prop('checked', true);
    } else {
        $('#select-all-events').prop('indeterminate', true).prop('checked', false);
    }
}

function updateBulkActionsVisibility() {
    const selectedCount = $('.event-checkbox:checked').length;
    const bulkActions = $('#bulk-actions');
    const selectedCountSpan = $('#selected-count');

    if (selectedCount > 0) {
        selectedCountSpan.text(`${selectedCount} selected`);
        bulkActions.show();
    } else {
        selectedCountSpan.text('0 selected');
        bulkActions.hide();
        // Reset bulk status dropdown
        $('#bulk-status').val('');
    }
}

function bulkUpdateStatus(eventIds, status) {
    if (!eventIds || eventIds.length === 0) {
        show_toastr('error', 'No events selected');
        return;
    }

    $.ajax({
        url: '{{ route("calendar-events.bulk-update-status") }}',
        method: 'POST',
        data: {
            event_ids: eventIds,
            status: status,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                show_toastr('success', response.message);
                // Reset selections and reload events
                $('.event-checkbox').prop('checked', false);
                $('#select-all-events').prop('checked', false);
                updateBulkActionsVisibility();
                loadEventsList(currentPage, currentPerPage);
                loadAllEvents(); // Refresh sidebar events
                if (calendar) {
                    calendar.refetchEvents();
                }
            } else {
                show_toastr('error', response.message || 'Failed to update events');
            }
        },
        error: function(xhr) {
            console.error('Error updating events:', xhr);
            show_toastr('error', 'Failed to update events');
        }
    });
}

function bulkDeleteEvents(eventIds) {
    if (!eventIds || eventIds.length === 0) {
        show_toastr('error', 'No events selected');
        return;
    }

    // Show confirmation dialog
    if (!confirm(`Are you sure you want to delete ${eventIds.length} selected event(s)? This action cannot be undone.`)) {
        return;
    }

    $.ajax({
        url: '{{ route("calendar-events.bulk-delete") }}',
        method: 'POST',
        data: {
            event_ids: eventIds,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                show_toastr('success', response.message);
                // Reset selections and reload events
                $('.event-checkbox').prop('checked', false);
                $('#select-all-events').prop('checked', false);
                updateBulkActionsVisibility();
                loadEventsList(currentPage, currentPerPage);
                loadAllEvents(); // Refresh sidebar events
                if (calendar) {
                    calendar.refetchEvents();
                }
            } else {
                show_toastr('error', response.message || 'Failed to delete events');
            }
        },
        error: function(xhr) {
            console.error('Error deleting events:', xhr);
            show_toastr('error', 'Failed to delete events');
        }
    });
}

// View bookings for a specific event
function viewBookings(eventId) {
    $.ajax({
        url: '{{ route("appointments.index") }}',
        method: 'GET',
        data: {
            event_id: eventId
        },
        success: function(response) {
            if (response.success && response.data) {
                let bookingsHtml = '<div class="table-responsive"><table class="table table-sm">';
                bookingsHtml += '<thead><tr><th>Contact Name</th><th>Date</th><th>Time</th><th>Timezone</th></tr></thead><tbody>';

                if (response.data.length > 0) {
                    response.data.forEach(function(booking) {
                        const timezone = booking.custom_fields && booking.custom_fields.timezone ? booking.custom_fields.timezone : 'Not specified';
                        bookingsHtml += `
                            <tr>
                                <td>${booking.name}</td>
                                <td>${new Date(booking.date).toLocaleDateString()}</td>
                                <td>${booking.time}</td>
                                <td><small>${timezone}</small></td>
                            </tr>
                        `;
                    });
                } else {
                    bookingsHtml += '<tr><td colspan="4" class="text-center">No bookings found</td></tr>';
                }

                bookingsHtml += '</tbody></table></div>';

                // Show in a modal or alert
                const modal = `
                    <div class="modal fade" id="bookingsModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Event Bookings</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    ${bookingsHtml}
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Remove existing modal if any
                $('#bookingsModal').remove();

                // Add modal to body and show
                $('body').append(modal);
                $('#bookingsModal').modal('show');
            }
        },
        error: function(xhr) {
            alert('Error loading bookings: ' + xhr.responseText);
        }
    });
}

function collectDateOverrideData() {
    const overrides = [];
    document.querySelectorAll('input[name="date_override[]"]').forEach(input => {
        if (input.value) {
            overrides.push(input.value);
        }
    });
    return overrides;
}





// View event details in modal
function viewEvent(eventId) {
    console.log('Loading event details for ID:', eventId);

    // Validate eventId
    if (!eventId) {
        console.error('No event ID provided');
        showEventError('Invalid event ID');
        return;
    }

    // Show loading state
    $('#view-event-title').text('Loading...');
    $('#view-event-duration').text('');
    $('#view-event-booking-slots').text('');
    $('#view-event-notice').text('');
    $('#view-description-row').hide();
    $('#view-location-section').hide();
    $('#viewEventModal').modal('show');

    // First, try to get the event from the calendar events index
    $.ajax({
        url: '{{ route("calendar-events.index") }}',
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        timeout: 10000, // 10 second timeout
        success: function(response) {
            console.log('Events response:', response);

            if (response.success && response.data && Array.isArray(response.data)) {
                // Find the specific event by ID
                const event = response.data.find(e => e.id == eventId);

                if (event) {
                    console.log('Found event:', event);
                    populateEventModal(event, eventId);
                } else {
                    console.error('Event not found in response. Available events:', response.data.map(e => e.id));
                    showEventError('Event not found. The event may have been deleted or you may not have permission to view it.');
                }
            } else if (response.success === false) {
                console.error('API returned error:', response.message);
                showEventError(response.message || 'Failed to load event details');
            } else {
                console.error('Invalid response format:', response);
                showEventError('Invalid response from server');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX error:', {xhr, status, error});

            let errorMessage = 'Error loading event details';

            if (xhr.status === 403) {
                errorMessage = 'You do not have permission to view this event';
            } else if (xhr.status === 404) {
                errorMessage = 'Event not found';
            } else if (xhr.status === 500) {
                errorMessage = 'Server error occurred';
            } else if (status === 'timeout') {
                errorMessage = 'Request timed out. Please try again.';
            } else if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }

            showEventError(errorMessage);
        }
    });
}

// Helper function to populate the event modal
function populateEventModal(event, eventId) {
    try {
        // Validate event object
        if (!event || typeof event !== 'object') {
            console.error('Invalid event object:', event);
            showEventError('Invalid event data');
            return;
        }

        // Check if modal elements exist
        const requiredElements = [
            '#view-event-title',
            '#view-event-duration', '#view-event-booking-slots', '#view-event-notice'
        ];

        for (const elementId of requiredElements) {
            if ($(elementId).length === 0) {
                console.error('Required modal element not found:', elementId);
                showEventError('Modal elements not found. Please refresh the page.');
                return;
            }
        }

        // Set the event ID for copying the link
        $('#viewEventModal').data('event-id', eventId);

        // Populate basic event details
        $('#view-event-title').text(event.title || 'Untitled Event');



        // Event settings with safe defaults
        const duration = event.duration || 60;
        const bookingSlots = event.booking_per_slot || 1;
        const minimumNotice = event.minimum_notice || 0;

        $('#view-event-duration').text(duration + ' minutes');
        $('#view-event-booking-slots').text(bookingSlots);
        $('#view-event-notice').text(getNoticeText(minimumNotice));

        // Description
        if (event.description && typeof event.description === 'string' && event.description.trim()) {
            $('#view-event-description').html(event.description);
            $('#view-description-row').show();
        } else {
            $('#view-description-row').hide();
        }

        // Location details - handle new multi-location system
        let hasLocations = false;

        if (event.locations_data) {
            // New multi-location system
            try {
                const locations = JSON.parse(event.locations_data);
                if (locations && locations.length > 0) {
                    hasLocations = true;

                    // Display all locations
                    const locationDisplay = locations.map(loc => loc.display).join(', ');
                    $('#view-event-location').text(locationDisplay);
                    $('#view-location-section').show();

                    // Show details for each location type
                    let hasAddress = false;
                    let hasLink = false;
                    let addressText = '';
                    let linkText = '';
                    let linkHref = '#';

                    locations.forEach(location => {
                        if (location.type === 'in_person' && location.value) {
                            hasAddress = true;
                            addressText += (addressText ? '\n' : '') + location.value;
                        } else if (['zoom', 'meet', 'skype', 'phone', 'others'].includes(location.type) && location.value) {
                            hasLink = true;
                            linkText += (linkText ? ', ' : '') + location.value;
                            // Format phone numbers as tel: links
                            if (linkHref === '#') {
                                if (location.type === 'phone' && /^[\+]?[0-9\s\-\(\)]+$/.test(location.value.trim())) {
                                    linkHref = 'tel:' + location.value.trim();
                                } else {
                                    linkHref = location.value;
                                }
                            }
                        }
                    });

                    // Show address if any in-person locations
                    if (hasAddress) {
                        $('#view-event-address').text(addressText);
                        $('#view-address-row').show();
                    } else {
                        $('#view-address-row').hide();
                    }

                    // Show all online locations
                    const onlineLocations = locations.filter(loc => ['zoom', 'meet', 'skype', 'phone', 'others'].includes(loc.type));
                    if (onlineLocations.length > 0) {
                        const locationIcons = {
                            'zoom': 'ti ti-video',
                            'meet': 'ti ti-brand-google',
                            'skype': 'ti ti-brand-skype',
                            'phone': 'ti ti-phone',
                            'others': 'ti ti-dots'
                        };

                        let meetingLocationsHtml = '';
                        onlineLocations.forEach((location, index) => {
                            const icon = locationIcons[location.type] || 'ti ti-link';
                            const href = location.type === 'phone' && /^[\+]?[0-9\s\-\(\)]+$/.test(location.value.trim())
                                ? 'tel:' + location.value.trim()
                                : location.value;

                            const chipClass = `modern-location-chip ${location.type}-chip`;

                            meetingLocationsHtml += `
                                <a href="${href}" target="_blank" class="${chipClass}" onclick="handleMeetingLinkClick(event)">
                                    <i class="location-icon ${icon}"></i>
                                    <span class="location-name">${location.display || location.type}</span>
                                </a>
                            `;
                        });

                        $('#view-meeting-locations-container').html(meetingLocationsHtml);
                        $('#view-link-row').show();
                    } else {
                        $('#view-link-row').hide();
                    }

                    // Set dynamic column classes based on what's visible
                    updateLocationColumnLayout();
                }
            } catch (e) {
                console.error('Error parsing locations_data:', e);
                $('#view-event-location').text('Location data error');
                $('#view-location-section').show();
                $('#view-address-row').hide();
                $('#view-link-row').hide();
                hasLocations = true;
            }
        } else if (event.location && typeof event.location === 'string') {
            // Legacy single location system
            hasLocations = true;
            const locationLabels = {
                'in_person': 'In Person',
                'zoom': 'Zoom',
                'skype': 'Skype',
                'meet': 'Google Meet',
                'phone': 'Phone Call',
                'others': 'Others'
            };

            $('#view-event-location').text(locationLabels[event.location] || event.location);
            $('#view-location-section').show();

            // Handle physical address or meeting link
            if (event.location === 'in_person') {
                if (event.physical_address && typeof event.physical_address === 'string' && event.physical_address.trim()) {
                    $('#view-event-address').text(event.physical_address);
                    $('#view-address-row').show();
                } else {
                    $('#view-event-address').text('Address not specified');
                    $('#view-address-row').show();
                }
                $('#view-link-row').hide();
            } else {
                // For online meetings and phone calls
                const locationIcons = {
                    'zoom': 'ti ti-video',
                    'meet': 'ti ti-brand-google',
                    'skype': 'ti ti-brand-skype',
                    'phone': 'ti ti-phone',
                    'others': 'ti ti-dots'
                };

                let meetingLocationsHtml = '';
                const icon = locationIcons[event.location] || 'ti ti-link';
                const locationName = locationLabels[event.location] || event.location;

                const chipClass = `modern-location-chip ${event.location}-chip`;

                if (event.meet_link && typeof event.meet_link === 'string' && event.meet_link.trim() && event.meet_link !== '#') {
                    const meetLink = event.meet_link.trim();
                    const href = /^[\+]?[0-9\s\-\(\)]+$/.test(meetLink)
                        ? 'tel:' + meetLink
                        : meetLink;

                    meetingLocationsHtml = `
                        <a href="${href}" target="_blank" class="${chipClass}" onclick="handleMeetingLinkClick(event)">
                            <i class="location-icon ${icon}"></i>
                            <span class="location-name">${locationName}</span>
                        </a>
                    `;
                } else {
                    meetingLocationsHtml = `
                        <div class="${chipClass}" style="cursor: default; opacity: 0.7;">
                            <i class="location-icon ${icon}"></i>
                            <span class="location-name">${locationName}</span>
                            <small class="ms-2 text-muted">(No link)</small>
                        </div>
                    `;
                }

                $('#view-meeting-locations-container').html(meetingLocationsHtml);
                $('#view-link-row').show();
                $('#view-address-row').hide();

                // Set dynamic column classes
                updateLocationColumnLayout();
            }
        }

        if (!hasLocations) {
            $('#view-location-section').hide();
        }

        console.log('Event modal populated successfully');

    } catch (error) {
        console.error('Error populating event modal:', error);
        showEventError('Error displaying event details: ' + error.message);
    }
}

// Helper function to update location column layout dynamically
function updateLocationColumnLayout() {
    const hasAddress = $('#view-address-row').is(':visible');
    const hasOnlineLocations = $('#view-link-row').is(':visible');

    // Remove existing column classes
    $('#view-address-row, #view-link-row').removeClass('col-12 col-md-6 col-lg-4');

    if (hasAddress && hasOnlineLocations) {
        // Both types present - use two columns
        $('#view-address-row, #view-link-row').addClass('col-md-6');
    } else if (hasAddress || hasOnlineLocations) {
        // Only one type present - use full width
        $('#view-address-row, #view-link-row').addClass('col-12');
    }
}

// Helper function to show error in modal
function showEventError(message) {
    $('#view-event-title').text('Error');
    $('#view-event-duration').text('N/A');
    $('#view-event-booking-slots').text('N/A');
    $('#view-event-notice').text('N/A');
    $('#view-description-row').hide();
    $('#view-location-section').hide();
}

// Helper function to format notice text
function getNoticeText(minutes) {
    // Handle null, undefined, or invalid values
    if (!minutes || isNaN(minutes) || minutes <= 0) {
        return 'No notice required';
    }

    // Convert to number if it's a string
    const numMinutes = parseInt(minutes);

    if (isNaN(numMinutes) || numMinutes <= 0) {
        return 'No notice required';
    }

    const hours = Math.floor(numMinutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
        const remainingHours = hours % 24;
        if (remainingHours > 0) {
            return `${days} day${days > 1 ? 's' : ''} ${remainingHours} hour${remainingHours > 1 ? 's' : ''}`;
        }
        return `${days} day${days > 1 ? 's' : ''}`;
    }

    if (hours > 0) {
        const remainingMinutes = numMinutes % 60;
        if (remainingMinutes > 0) {
            return `${hours} hour${hours > 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes > 1 ? 's' : ''}`;
        }
        return `${hours} hour${hours > 1 ? 's' : ''}`;
    }

    return `${numMinutes} minute${numMinutes > 1 ? 's' : ''}`;
}

// Delete event
function deleteEvent(eventId) {
    if (confirm('Are you sure you want to delete this event?')) {
        $.ajax({
            url: `{{ url('calendar-events') }}/${eventId}`,
            method: 'DELETE',
            data: {
                "_token": "{{ csrf_token() }}"
            },
            success: function(response) {
                if (response.success) {
                    show_toastr('success', 'Event deleted successfully!');
                    loadEventsList(currentPage, currentPerPage);
                    loadAllEvents(); // Refresh sidebar events
                    if (calendar) {
                        calendar.refetchEvents();
                    }
                } else {
                    show_toastr('error', response.message || 'Failed to delete event');
                }
            },
            error: function(xhr) {
                console.log('Delete error:', xhr.responseText);
                show_toastr('error', 'Error deleting event. Please try again.');
            }
        });
    }
}

// Make functions global
window.openCreateEventModal = openCreateEventModal;
window.switchView = switchView;
window.deleteEvent = deleteEvent;
window.viewEvent = viewEvent;
window.editEvent = editEvent;
window.toggleLocationFields = toggleLocationFields;
window.collectDateOverrideData = collectDateOverrideData;
window.showCopyEventToast = showCopyEventToast;
window.hideCopyEventToast = hideCopyEventToast;
window.filterAppointments = filterAppointments;
window.applyAppointmentFilter = applyAppointmentFilter;

// Test function for copy event link
window.testCopyEventLink = function() {
    console.log('Testing copy event link functionality...');
    console.log('copyEventLinkFromList function exists:', typeof window.copyEventLinkFromList);

    // Test the copy function with a dummy event ID
    if (typeof window.copyEventLinkFromList === 'function') {
        console.log('Testing copyEventLinkFromList with dummy data...');
        // Create a temporary test button
        const testBtn = document.createElement('button');
        testBtn.innerHTML = '<i class="ti ti-copy"></i>';
        window.copyEventLinkFromList(123, testBtn);
    }
};

//weekly available
// Initialize with one slot per day when enabled
$('.day-availability input[type="checkbox"]').change(function() {
    const day = $(this).closest('.day-availability').find('.day-slots');
    if (this.checked) {
        day.show();
        if (day.find('.time-slot').length === 0) {
            // Get the day name from the checkbox id (e.g., 'monday-checkbox' -> 'monday')
            const dayName = $(this).attr('id').replace('-checkbox', '');
            addTimeSlot(dayName);
        }
    } else {
        day.hide();
    }
});

// Add slot functionality - removed duplicate handler to prevent multiple slots being added

// Remove slot functionality - allow removal of any slot without restrictions
$(document).on('click', '.remove-slot-btn', function() {
    $(this).closest('.time-slot').remove();
});

// Removed duplicate addNewSlot function - using addTimeSlot instead

//date override
let unavailableSlots = [];

// Function to toggle the visibility of the date override field
function toggleDateOverride() {
    const overrideField = document.getElementById('date-override-field');
    if (overrideField.style.display === 'none') {
        overrideField.style.display = 'block';
    } else {
        overrideField.style.display = 'none';
    }
}

// Function to add the selected date and time as an unavailable slot
function addUnavailableSlot() {
    const dateInput = document.getElementById('override_date');
    const timeSelect = document.getElementById('override_time');
    const container = document.getElementById('unavailable-slots-list');

    const dateValue = dateInput.value;
    const timeValue = timeSelect.value;

    if (!dateValue) {
        alert("Please select a date.");
        return;
    }

    if (!timeValue) {
        alert("Please select a time.");
        return;
    }

    // Combine date and time into datetime-local format
    const datetimeValue = `${dateValue}T${timeValue}`;

    // Check for duplicates
    const exists = [...document.querySelectorAll('input[name="date_override[]"]')]
        .some(i => i.value === datetimeValue);
    if (exists) {
        alert("This date and time slot is already added.");
        return;
    }

    const wrapper = document.createElement('div');
    wrapper.className = 'd-flex align-items-center mb-2 gap-2';

    // Format display text
    const dateObj = new Date(datetimeValue);
    const readable = dateObj.toLocaleString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });

    wrapper.innerHTML = `
        <input type="hidden" name="date_override[]" value="${datetimeValue}">
        <span class="badge bg-danger flex-grow-1 py-2 px-3">
            <i class="ti ti-calendar-x me-1"></i>
            ${readable}
        </span>
        <button type="button" class="btn btn-sm btn-outline-danger" onclick="this.parentElement.remove()" title="Remove override">
            <i class="ti ti-trash"></i>
        </button>
    `;

    container.appendChild(wrapper);

    // Reset inputs
    dateInput.value = '';
    timeSelect.value = '';
}

// Function to populate date override when editing an event
function populateDateOverride(datetimeValue) {
    const container = document.getElementById('unavailable-slots-list');

    const wrapper = document.createElement('div');
    wrapper.className = 'd-flex align-items-center mb-2 gap-2';

    // Format display text
    const dateObj = new Date(datetimeValue);
    const readable = dateObj.toLocaleString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });

    wrapper.innerHTML = `
        <input type="hidden" name="date_override[]" value="${datetimeValue}">
        <span class="badge bg-danger flex-grow-1 py-2 px-3">
            <i class="ti ti-calendar-x me-1"></i>
            ${readable}
        </span>
        <button type="button" class="btn btn-sm btn-outline-danger" onclick="this.parentElement.remove()" title="Remove override">
            <i class="ti ti-trash"></i>
        </button>
    `;

    container.appendChild(wrapper);
}


// Function to update the unavailable slots list
function updateUnavailableSlotsList() {
    const slotsList = document.getElementById('unavailable-slots-list');
    slotsList.innerHTML = ''; // Clear the list

    unavailableSlots.forEach((slot, index) => {
        const listItem = document.createElement('li');
        listItem.textContent = slot.toLocaleString(); // Format the date for display

        // Create a delete button for each slot
        const deleteButton = document.createElement('button');
        deleteButton.textContent = 'Delete';
        deleteButton.className = 'btn btn-danger btn-sm ms-2';
        deleteButton.onclick = () => deleteUnavailableSlot(index); // Bind delete function

        listItem.appendChild(deleteButton);
        slotsList.appendChild(listItem);
    });
}

// Function to delete an unavailable slot
function deleteUnavailableSlot(index) {
    unavailableSlots.splice(index, 1); // Remove the slot from the array
    updateUnavailableSlotsList(); // Update the displayed list
    alert('The selected time slot has been removed.');
}

// Function to check if a selected time slot is available for booking
function isTimeSlotAvailable(selectedDate) {
    return !unavailableSlots.some(slot => slot.getTime() === selectedDate.getTime());
}

// Example function to create an appointment
function createAppointment(selectedDate) {
    if (!isTimeSlotAvailable(selectedDate)) {
        alert('This time slot is unavailable. Please choose a different time.');
        return;
    }

    // Proceed with appointment creation logic
    // For example, you can send the appointment data to the server here
    alert(`Appointment created for ${selectedDate.toLocaleString()}`);
}

// Show custom toast for copy event link
function showCopyEventToast(message = 'Event link copied!') {
    console.log('showCopyEventToast called with message:', message);

    const toast = document.getElementById('copy-event-toast');
    const msg = document.getElementById('copy-event-toast-message');

    if (toast && msg) {
        msg.textContent = message;
        toast.style.display = 'block';
        toast.setAttribute('aria-hidden', 'false');
        // Auto-hide after 2.5s
        clearTimeout(window._copyEventToastTimeout);
        window._copyEventToastTimeout = setTimeout(hideCopyEventToast, 2500);
        console.log('Toast displayed successfully');
    } else {
        // Fallback to alert if toast elements not found
        console.log('Toast elements not found, using alert fallback');
        alert(message);
    }
}
function hideCopyEventToast() {
    const toast = document.getElementById('copy-event-toast');
    if (toast) {
        toast.style.display = 'none';
        toast.setAttribute('aria-hidden', 'true');
    }
}
// Copy event link function for Events List (with direct event ID)
window.copyEventLinkFromList = function(eventId, btn) {
    console.log('copyEventLinkFromList called with eventId:', eventId, 'button:', btn);

    if (!eventId) {
        console.error('No event ID provided');
        showCopyEventToast('No event ID provided');
        updateCopyButtonList(btn, false);
        return;
    }

    // Copy the event page link
    const linkToCopy = `${window.location.origin}/calendar-events/${eventId}`;
    const successMessage = 'Event link copied!';

    console.log('Copying link:', linkToCopy);

    // Check if clipboard API is available and we're in a secure context
    const isSecureContext = window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost';
    console.log('Secure context:', isSecureContext);
    console.log('Clipboard API available:', !!navigator.clipboard);

    if (!navigator.clipboard || !isSecureContext) {
        console.log('Using fallback copy method');
        // Fallback for older browsers or non-secure contexts
        fallbackCopyTextToClipboardList(linkToCopy, btn, successMessage);
        return;
    }

    // Use modern clipboard API
    console.log('Using modern clipboard API');
    navigator.clipboard.writeText(linkToCopy).then(function() {
        console.log('Link copied successfully');
        showCopyEventToast(successMessage);
        updateCopyButtonList(btn, true);
    }).catch(function(err) {
        console.error('Failed to copy link with clipboard API:', err);
        console.log('Falling back to legacy copy method');
        // Try fallback method if clipboard API fails
        fallbackCopyTextToClipboardList(linkToCopy, btn, successMessage);
    });
}

// Enhanced copyEventLink function with better error handling (for modal - kept for backward compatibility)
window.copyEventLink = function(btn) {
    console.log('copyEventLink called with button:', btn);
    console.log('Button element:', $(btn));
    console.log('Modal element:', $('#viewEventModal'));

    // Get current event ID from the modal
    const eventId = $('#viewEventModal').data('event-id');
    console.log('Event ID from modal:', eventId);
    console.log('Modal data attributes:', $('#viewEventModal').data());

    if (!eventId) {
        console.error('No event ID found');
        showCopyEventToast('No event selected to copy link');
        updateCopyButton(btn, false);
        return;
    }

    // Copy the event page link
    const linkToCopy = `${window.location.origin}/calendar-events/${eventId}`;
    const successMessage = 'Event link copied!';

    console.log('Copying link:', linkToCopy);

    // Check if clipboard API is available and we're in a secure context
    const isSecureContext = window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost';
    console.log('Secure context:', isSecureContext);
    console.log('Clipboard API available:', !!navigator.clipboard);

    if (!navigator.clipboard || !isSecureContext) {
        console.log('Using fallback copy method');
        // Fallback for older browsers or non-secure contexts
        fallbackCopyTextToClipboard(linkToCopy, btn, successMessage);
        return;
    }

    // Use modern clipboard API
    console.log('Using modern clipboard API');
    navigator.clipboard.writeText(linkToCopy).then(function() {
        console.log('Link copied successfully');
        showCopyEventToast(successMessage);
        updateCopyButton(btn, true);
    }).catch(function(err) {
        console.error('Failed to copy link with clipboard API:', err);
        console.log('Falling back to legacy copy method');
        // Try fallback method if clipboard API fails
        fallbackCopyTextToClipboard(linkToCopy, btn, successMessage);
    });
}

// Fallback function for older browsers
function fallbackCopyTextToClipboard(text, btn, successMessage) {
    console.log('Using fallback copy method for text:', text);

    const textArea = document.createElement("textarea");
    textArea.value = text;

    // Avoid scrolling to bottom
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        console.log('execCommand copy result:', successful);

        if (successful) {
            console.log('Fallback copy successful');
            showCopyEventToast(successMessage);
            updateCopyButton(btn, true);
        } else {
            console.log('Fallback copy failed');
            showCopyEventToast('Failed to copy link - please copy manually: ' + text);
            updateCopyButton(btn, false);
        }
    } catch (err) {
        console.error('Fallback: Could not copy text: ', err);
        showCopyEventToast('Copy not supported - Link: ' + text);
        updateCopyButton(btn, false);
    }

    document.body.removeChild(textArea);
}

// Fallback function for older browsers (Events List)
function fallbackCopyTextToClipboardList(text, btn, successMessage) {
    console.log('Using fallback copy method for list with text:', text);

    const textArea = document.createElement("textarea");
    textArea.value = text;

    // Avoid scrolling to bottom
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        console.log('execCommand copy result for list:', successful);

        if (successful) {
            console.log('Fallback copy successful for list');
            showCopyEventToast(successMessage);
            updateCopyButtonList(btn, true);
        } else {
            console.log('Fallback copy failed for list');
            showCopyEventToast('Failed to copy link - please copy manually: ' + text);
            updateCopyButtonList(btn, false);
        }
    } catch (err) {
        console.error('Fallback: Could not copy text for list: ', err);
        showCopyEventToast('Copy not supported - Link: ' + text);
        updateCopyButtonList(btn, false);
    }

    document.body.removeChild(textArea);
}

// Helper function to update copy button appearance
function updateCopyButton(btn, success) {
    if (!btn) return;

    const $btn = $(btn);
    const originalHtml = $btn.html();

    if (success) {
        $btn.html('<i class="ti ti-check me-1"></i>Copied!')
            .removeClass('btn-outline-primary')
            .addClass('btn-success copy-link-dark-green')
            .prop('disabled', true);
    } else {
        $btn.html('<i class="ti ti-x me-1"></i>Failed')
            .removeClass('btn-outline-primary')
            .addClass('btn-danger')
            .prop('disabled', true);
    }

    // Reset button after 3 seconds
    setTimeout(function() {
        $btn.html(originalHtml)
            .removeClass('btn-success btn-danger copy-link-dark-green')
            .addClass('btn-outline-primary')
            .prop('disabled', false);
    }, 3000);
}

// Helper function to update copy button appearance for Events List
function updateCopyButtonList(btn, success) {
    if (!btn) return;

    const originalHtml = btn.innerHTML;
    const originalStyle = btn.style.background;

    if (success) {
        btn.innerHTML = '<i class="ti ti-check"></i>';
        btn.style.background = 'linear-gradient(to right, #198754, #20c997)';
        btn.disabled = true;
    } else {
        btn.innerHTML = '<i class="ti ti-x"></i>';
        btn.style.background = 'linear-gradient(to right, #dc3545, #fd7e14)';
        btn.disabled = true;
    }

    // Reset button after 3 seconds
    setTimeout(function() {
        btn.innerHTML = originalHtml;
        btn.style.background = originalStyle;
        btn.disabled = false;
    }, 3000);
}

// Handle meeting link click with validation
function handleMeetingLinkClick(event) {
    const link = event.target.closest('a').getAttribute('href');

    if (!link || link === '#' || link.trim() === '') {
        event.preventDefault();
        return false;
    }

    // Check if it's a phone number (starts with tel:)
    if (link.startsWith('tel:')) {
        // Allow default behavior for tel: links (will open phone app)
        return true;
    }

    // Validate if it's a proper URL
    try {
        new URL(link);
        // If URL is valid, allow the default behavior (open in new tab)
        return true;
    } catch (e) {
        event.preventDefault();
        return false;
    }
}

// Bookings Management Functions
function createNewBookingAction() {
    // Switch to calendar view and show a helpful message
    switchView('calendar');

    // Show a toast or alert to guide the user
    setTimeout(() => {
        showBookingsAlert('{{ __("To create a new booking, click on a date in the calendar and select an available time slot.") }}', 'info');
    }, 500);
}

function refreshBookings() {
    console.log('Refreshing bookings...');
    loadBookings();
}

// Global variable to track current appointment filter
let currentAppointmentFilter = 'upcoming'; // Default to upcoming
let allBookingsData = []; // Store all bookings for client-side filtering

function loadBookings() {
    console.log('Loading bookings...');

    // Show loading state
    $('#bookings-tbody').html(`
        <tr>
            <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '11' : '10' }}" class="text-center py-4">
                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                {{ __('Loading bookings...') }}
            </td>
        </tr>
    `);

    // Get date filter values
    const startDate = $('#filter-start-date').val();
    const endDate = $('#filter-end-date').val();

    // Build URL with date filters (without appointment filter for now)
    let url = '{{ route("bookings.index") }}';
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    if (params.toString()) url += '?' + params.toString();

    // Load all bookings and then filter client-side
    $.ajax({
        url: url,
        method: 'GET',
        success: function(response) {
            let tbody = $('#bookings-tbody');
            tbody.empty();

            try {
                // Parse the HTML response to extract booking data
                let $response = $(response);
                let $bookingRows = $response.find('#bookings-table tbody tr');

                if ($bookingRows.length > 0) {
                    // Check if it's the "no bookings" row
                    let firstRowText = $bookingRows.first().text().trim();
                    if (firstRowText.includes('No bookings found') || firstRowText.includes('{{ __("No bookings found") }}')) {
                        tbody.html(`
                            <tr>
                                <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '11' : '10' }}" class="text-center">{{ __('No bookings found') }}</td>
                            </tr>
                        `);
                        allBookingsData = [];
                    } else {
                        // Store all booking rows for filtering
                        allBookingsData = [];
                        
                        $bookingRows.each(function(index) {
                            let $row = $(this);
                            let bookingId = extractBookingId($row);
                            let bookingDate = extractBookingDate($row);
                            let bookingStatus = extractBookingStatus($row);
                            
                            if (bookingId) {
                                // Add checkbox as first column
                                let checkboxHtml = `
                                    <td class="checkbox-column">
                                        <div class="form-check">
                                            <input class="form-check-input booking-checkbox" type="checkbox" value="${bookingId}" id="booking-${bookingId}">
                                            <label class="form-check-label" for="booking-${bookingId}"></label>
                                        </div>
                                    </td>
                                `;
                                $row.prepend(checkboxHtml);
                                
                                // Store booking data for filtering
                                allBookingsData.push({
                                    id: bookingId,
                                    date: bookingDate,
                                    status: bookingStatus,
                                    row: $row
                                });
                            }
                        });
                        
                        // Apply current filter
                        applyAppointmentFilter();
                        
                        // Ensure column visibility is applied after loading data
                        applyColumnVisibility();
                    }
                } else {
                    tbody.html(`
                        <tr>
                            <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '11' : '10' }}" class="text-center">{{ __('No bookings found') }}</td>
                        </tr>
                    `);
                    allBookingsData = [];
                }
            } catch (error) {
                console.error('Error parsing bookings data:', error);
                tbody.html(`
                    <tr>
                        <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '11' : '10' }}" class="text-center">
                            <div class="alert alert-info">
                                <i class="ti ti-info-circle me-2"></i>
                                {{ __('Unable to load bookings dynamically. Please') }}
                                <a href="{{ route('bookings.index') }}" target="_blank" class="alert-link">{{ __('click here') }}</a>
                                {{ __('to view bookings in a new tab.') }}
                            </div>
                        </td>
                    </tr>
                `);
            }
        },
        error: function(xhr) {
            console.error('Error loading bookings:', xhr);
            let tbody = $('#bookings-tbody');

            if (xhr.status === 403) {
                tbody.html(`
                    <tr>
                        <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '11' : '10' }}" class="text-center">
                            <div class="alert alert-warning">
                                <i class="ti ti-lock me-2"></i>
                                {{ __('You do not have permission to view bookings.') }}
                            </div>
                        </td>
                    </tr>
                `);
            } else {
                tbody.html(`
                    <tr>
                        <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '11' : '10' }}" class="text-center">
                            <div class="alert alert-danger">
                                <i class="ti ti-exclamation-triangle me-2"></i>
                                {{ __('Error loading bookings. Please') }}
                                <a href="{{ route('bookings.index') }}" target="_blank" class="alert-link">{{ __('click here') }}</a>
                                {{ __('to view bookings in a new tab.') }}
                            </div>
                        </td>
                    </tr>
                `);
            }
        }
    });
}

// Function to apply column visibility based on current filter
function applyColumnVisibility() {
    if (currentAppointmentFilter === 'upcoming' || currentAppointmentFilter === 'past' || currentAppointmentFilter === 'cancelled') {
        $('.payment-column').hide();
        $('.regular-column').show();
        // Show checkboxes and action buttons for regular views
        $('.checkbox-column').show(); // Checkbox column
        $('.actions-column').show(); // Actions column
        console.log('Applied column visibility: Hiding payment columns for filter:', currentAppointmentFilter);
    } else if (currentAppointmentFilter === 'payments') {
        $('.regular-column').hide();
        $('.payment-column').show();
        // Hide checkboxes and action buttons for payments view
        $('.checkbox-column').hide(); // Checkbox column
        $('.actions-column').hide(); // Actions column
        console.log('Applied column visibility: Showing payment columns for filter:', currentAppointmentFilter);
    }
}

// Function to filter appointments by type (upcoming/past/cancelled)
function filterAppointments(filterType) {
    console.log('Filtering appointments by:', filterType);
    
    // Update global filter state
    currentAppointmentFilter = filterType;
    
    // Update button states
    $('#upcoming-filter-btn, #past-filter-btn, #cancelled-filter-btn, #payments-filter-btn').removeClass('btn-primary').addClass('btn-outline-primary');

    if (filterType === 'upcoming') {
        $('#upcoming-filter-btn').removeClass('btn-outline-primary').addClass('btn-primary');
        console.log('Upcoming tab: Hiding payment columns');
        
        // Refresh data when switching from payment tab to ensure consistency
        if (currentAppointmentFilter === 'payments') {
            console.log('Refreshing data when switching from payment tab to upcoming tab...');
            loadBookings();
            return; // Exit early as loadBookings will call applyAppointmentFilter
        }
    } else if (filterType === 'past') {
        $('#past-filter-btn').removeClass('btn-outline-primary').addClass('btn-primary');
        console.log('Past tab: Hiding payment columns');
        
        // Refresh data when switching from payment tab to ensure consistency
        if (currentAppointmentFilter === 'payments') {
            console.log('Refreshing data when switching from payment tab to past tab...');
            loadBookings();
            return; // Exit early as loadBookings will call applyAppointmentFilter
        }
    } else if (filterType === 'cancelled') {
        $('#cancelled-filter-btn').removeClass('btn-outline-primary').addClass('btn-primary');
        console.log('Cancelled tab: Hiding payment columns');
        
        // Refresh data when switching from payment tab to ensure consistency
        if (currentAppointmentFilter === 'payments') {
            console.log('Refreshing data when switching from payment tab to cancelled tab...');
            loadBookings();
            return; // Exit early as loadBookings will call applyAppointmentFilter
        }
    } else if (filterType === 'payments') {
        $('#payments-filter-btn').removeClass('btn-outline-primary').addClass('btn-primary');
        console.log('Payment Details tab: Showing payment columns');
        // Filter to show only bookings with payment information
        filterType = 'all'; // Show all bookings but with payment columns
        
        // Show loading indicator for payment tab refresh
        $('#bookings-tbody').html(`
            <tr>
                <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '11' : '10' }}" class="text-center py-4">
                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                    {{ __('Refreshing payment data...') }}
                </td>
            </tr>
        `);
        
        // Refresh data when switching to payment tab to ensure latest payment information
        console.log('Refreshing data for payment tab to get latest payment information...');
        loadBookings();
        return; // Exit early as loadBookings will call applyAppointmentFilter
    }
    
    // Apply column visibility
    applyColumnVisibility();
    
    // Apply filter to existing data
    applyAppointmentFilter();
}

// Function to apply appointment filter to loaded data
function applyAppointmentFilter() {
    console.log('Applying appointment filter:', currentAppointmentFilter);
    console.log('Total bookings data:', allBookingsData.length);
    
    if (allBookingsData.length === 0) {
        return;
    }
    
    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0); // Set to start of day for comparison
    
    let filteredBookings = allBookingsData.filter(booking => {
        if (!booking.date) return true; // Include bookings without dates
        
        const bookingDate = new Date(booking.date);
        bookingDate.setHours(0, 0, 0, 0); // Set to start of day for comparison
        
        console.log(`Booking ${booking.id}: date=${booking.date}, status=${booking.status}, filter=${currentAppointmentFilter}`);
        
        if (currentAppointmentFilter === 'upcoming') {
            const isUpcoming = bookingDate >= currentDate && booking.status !== 'cancel';
            console.log(`  Upcoming check: ${bookingDate} >= ${currentDate} && ${booking.status} !== 'cancel' = ${isUpcoming}`);
            return isUpcoming;
        } else if (currentAppointmentFilter === 'past') {
            const isPast = bookingDate < currentDate && booking.status !== 'cancel';
            console.log(`  Past check: ${bookingDate} < ${currentDate} && ${booking.status} !== 'cancel' = ${isPast}`);
            return isPast;
        } else if (currentAppointmentFilter === 'cancelled') {
            const isCancelled = booking.status === 'cancel';
            console.log(`  Cancelled check: ${booking.status} === 'cancel' = ${isCancelled}`);
            return isCancelled;
        }
        
        return true; // Default: show all
    });
    
    // Clear tbody and add filtered rows
    let tbody = $('#bookings-tbody');
    tbody.empty();
    
    if (filteredBookings.length > 0) {
        filteredBookings.forEach(booking => {
            tbody.append(booking.row);
        });
    } else {
        let filterText = '';
        if (currentAppointmentFilter === 'upcoming') {
            filterText = 'upcoming';
        } else if (currentAppointmentFilter === 'past') {
            filterText = 'past';
        } else if (currentAppointmentFilter === 'cancelled') {
            filterText = 'canceled';
        }
        tbody.html(`
            <tr>
                <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '11' : '10' }}" class="text-center">
                    <div class="alert alert-info">
                        <i class="ti ti-info-circle me-2"></i>
                        {{ __('No') }} ${filterText} {{ __('appointments found') }}
                    </div>
                </td>
            </tr>
        `);
    }
    
    console.log(`Filtered ${allBookingsData.length} bookings to ${filteredBookings.length} ${currentAppointmentFilter} appointments`);
}

// Helper function to extract booking date from row
function extractBookingDate($row) {
    // Try to find the date in the Date column (usually 8th column)
    let dateText = '';
    
    // Look for date in various possible positions
    const dateCells = $row.find('td');
    
    // Try to find a cell that contains a date pattern
    dateCells.each(function() {
        const cellText = $(this).text().trim();
        // Look for date patterns like YYYY-MM-DD, DD/MM/YYYY, MM/DD/YYYY, etc.
        if (cellText.match(/\d{4}-\d{2}-\d{2}/) ||
            cellText.match(/\d{1,2}\/\d{1,2}\/\d{4}/) ||
            cellText.match(/\d{1,2}-\d{1,2}-\d{4}/)) {
            dateText = cellText;
            return false; // Break the loop
        }
    });
    
    if (!dateText) {
        // If no date pattern found, try the 8th column (Date column)
        if (dateCells.length >= 8) {
            dateText = dateCells.eq(7).text().trim(); // 0-indexed, so 7 is the 8th column
        }
    }
    
    // Parse the date
    if (dateText) {
        try {
            // Try different date formats
            let parsedDate = null;
            
            if (dateText.match(/\d{4}-\d{2}-\d{2}/)) {
                // YYYY-MM-DD format
                parsedDate = new Date(dateText);
            } else if (dateText.match(/\d{1,2}\/\d{1,2}\/\d{4}/)) {
                // MM/DD/YYYY or DD/MM/YYYY format
                parsedDate = new Date(dateText);
            } else if (dateText.match(/\d{1,2}-\d{1,2}-\d{4}/)) {
                // DD-MM-YYYY format
                const parts = dateText.split('-');
                if (parts.length === 3) {
                    parsedDate = new Date(parts[2], parts[1] - 1, parts[0]);
                }
            }
            
            if (parsedDate && !isNaN(parsedDate.getTime())) {
                return parsedDate.toISOString().split('T')[0]; // Return YYYY-MM-DD format
            }
        } catch (e) {
            console.warn('Error parsing date:', dateText, e);
        }
    }
    
    return null;
}

// Helper function to extract booking ID from row
function extractBookingId($row) {
    // Try to find booking ID from various sources in the row
    let bookingId = null;
    
    // Look for data attributes
    if ($row.data('booking-id')) {
        bookingId = $row.data('booking-id');
    }
    // Look for ID in the first cell (ID column)
    else if ($row.find('td').first().text().trim().match(/^\d+$/)) {
        bookingId = $row.find('td').first().text().trim();
    }
    // Look for ID in action buttons
    else {
        let actionButtons = $row.find('button[onclick*="viewBooking"], button[onclick*="editBooking"], button[onclick*="deleteBooking"]');
        if (actionButtons.length > 0) {
            let onclickAttr = actionButtons.first().attr('onclick');
            let match = onclickAttr.match(/\d+/);
            if (match) {
                bookingId = match[0];
            }
        }
    }
    
    return bookingId;
}

// Helper function to extract booking status from row
function extractBookingStatus($row) {
    // Try to find status from various sources in the row
    let status = 'scheduled'; // Default status
    
    // Look for status in the Status column (usually 9th column after adding checkbox)
    const statusCells = $row.find('td');
    if (statusCells.length >= 10) {
        const statusCell = statusCells.eq(9); // 0-indexed, so 9 is the 10th column (Status)
        
        // Look for the dropdown button text which contains the status name
        const statusButton = statusCell.find('button.dropdown-toggle');
        if (statusButton.length > 0) {
            const statusText = statusButton.text().trim().toLowerCase();
            
            // Map status text to status values based on the statusDisplay mapping
            if (statusText.includes('cancel')) {
                status = 'cancel';
            } else if (statusText.includes('show up')) {
                status = 'show_up';
            } else if (statusText.includes('no show')) {
                status = 'no_show';
            } else if (statusText.includes('reschedule')) {
                status = 'reschedule';
            } else if (statusText.includes('scheduled')) {
                status = 'scheduled';
            }
        } else {
            // Fallback: check the entire cell text
            const statusText = statusCell.text().trim().toLowerCase();
            
            // Map status text to status values
            if (statusText.includes('cancel') || statusText.includes('cancelled')) {
                status = 'cancel';
            } else if (statusText.includes('show up') || statusText.includes('show-up')) {
                status = 'show_up';
            } else if (statusText.includes('no show') || statusText.includes('no-show')) {
                status = 'no_show';
            } else if (statusText.includes('reschedule')) {
                status = 'reschedule';
            } else if (statusText.includes('scheduled') || statusText.includes('upcoming')) {
                status = 'scheduled';
            }
        }
    }
    
    // Also check for status in data attributes
    if ($row.data('status')) {
        status = $row.data('status');
    }
    
    return status;
}

// Bulk booking operations helper functions
function getSelectedBookingIds() {
    const selectedIds = [];
    $('.booking-checkbox:checked').each(function() {
        selectedIds.push(parseInt($(this).val()));
    });
    return selectedIds;
}

function updateBookingSelectAllState() {
    const totalCheckboxes = $('.booking-checkbox').length;
    const checkedCheckboxes = $('.booking-checkbox:checked').length;

    if (checkedCheckboxes === 0) {
        $('#select-all-bookings').prop('indeterminate', false).prop('checked', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
        $('#select-all-bookings').prop('indeterminate', false).prop('checked', true);
    } else {
        $('#select-all-bookings').prop('indeterminate', true).prop('checked', false);
    }
}

function updateBulkBookingActionsVisibility() {
    const selectedCount = $('.booking-checkbox:checked').length;
    const bulkActions = $('#bulk-booking-actions');
    const selectedCountSpan = $('#selected-booking-count');

    if (selectedCount > 0) {
        selectedCountSpan.text(`${selectedCount} `);
        bulkActions.show();
    } else {
        selectedCountSpan.text('0');
        bulkActions.hide();
    }
}

function bulkCancelBookings(bookingIds) {
    if (!bookingIds || bookingIds.length === 0) {
        showBookingsAlert('No appointments selected', 'warning');
        return;
    }

    // Show confirmation dialog
    if (!confirm(`Are you sure you want to cancel ${bookingIds.length} selected appointment(s)? This action cannot be undone.`)) {
        return;
    }

    // Show loading state
    $('#bulk-cancel-btn').prop('disabled', true).html('<i class="ti ti-loader me-1"></i>{{ __("Cancelling...") }}');

    $.ajax({
        url: '{{ route("bookings.bulk-cancel") }}',
        method: 'POST',
        data: {
            booking_ids: bookingIds,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                showBookingsAlert(response.message || 'Selected appointments cancelled successfully', 'success');
                // Reset selections and reload bookings
                $('.booking-checkbox').prop('checked', false);
                $('#select-all-bookings').prop('checked', false);
                updateBulkBookingActionsVisibility();
                loadBookings();
                
                // Refresh calendar if available
                if (calendar) {
                    calendar.refetchEvents();
                }
            } else {
                showBookingsAlert(response.message || 'Failed to cancel appointments', 'danger');
            }
        },
        error: function(xhr) {
            console.error('Error cancelling appointments:', xhr);
            let errorMessage = 'Failed to cancel appointments';
            
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                const errors = Object.values(xhr.responseJSON.errors).flat();
                errorMessage = errors.join(', ');
            }
            
            showBookingsAlert(errorMessage, 'danger');
        },
        complete: function() {
            // Re-enable button
            $('#bulk-cancel-btn').prop('disabled', false).html('<i class="ti ti-x me-1"></i>{{ __("Bulk Cancel") }}');
        }
    });
}

// Date Filter Functions
function applyDateFilter() {
    const startDate = $('#filter-start-date').val();
    const endDate = $('#filter-end-date').val();

    if (!startDate && !endDate) {
        showBookingsAlert('{{ __("Please select at least one date to filter.") }}', 'warning');
        return;
    }

    if (startDate && endDate && startDate > endDate) {
        showBookingsAlert('{{ __("Start date cannot be after end date.") }}', 'warning');
        return;
    }

    loadBookings();
}

function clearDateFilter() {
    $('#filter-start-date').val('');
    $('#filter-end-date').val('');
    loadBookings();
}

// Status Management Functions
function changeBookingStatus(bookingId, newStatus) {
    if (!bookingId || !newStatus) {
        showBookingsAlert('{{ __("Invalid booking or status.") }}', 'error');
        return;
    }

    // Show loading state
    const $statusCell = $(`#booking-${bookingId} .status-dropdown`);
    const originalContent = $statusCell.html();
    $statusCell.html('<div class="spinner-border spinner-border-sm" role="status"></div>');

    $.ajax({
        url: `/bookings/${bookingId}/status`,
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: {
            status: newStatus
        },
        success: function(response) {
            if (response.success) {
                showBookingsAlert('{{ __("Booking status updated successfully.") }}', 'success');
                // Refresh the bookings to show updated status
                loadBookings();
            } else {
                showBookingsAlert('{{ __("Failed to update booking status.") }}', 'error');
                $statusCell.html(originalContent);
            }
        },
        error: function(xhr) {
            console.error('Error updating booking status:', xhr);
            let errorMessage = '{{ __("Failed to update booking status.") }}';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            }
            showBookingsAlert(errorMessage, 'error');
            $statusCell.html(originalContent);
        }
    });
}

// Initialize default view on page load
$(document).ready(function() {
    // Set default active button styling
    switchView('calendar');

    // Handle per-page selector change
    $('#events-per-page').on('change', function() {
        const perPage = parseInt($(this).val());
        currentPerPage = perPage;
        loadEventsList(1, perPage); // Reset to first page when changing per-page
    });

    // Handle select all checkbox for events
    $('#select-all-events').on('change', function() {
        const isChecked = $(this).is(':checked');
        $('.event-checkbox').prop('checked', isChecked);
        updateBulkActionsVisibility();
    });

    // Handle individual checkbox changes for events
    $(document).on('change', '.event-checkbox', function() {
        updateSelectAllState();
        updateBulkActionsVisibility();
    });

    // Handle select all checkbox for bookings
    $('#select-all-bookings').on('change', function() {
        const isChecked = $(this).is(':checked');
        $('.booking-checkbox').prop('checked', isChecked);
        updateBulkBookingActionsVisibility();
    });

    // Handle individual checkbox changes for bookings
    $(document).on('change', '.booking-checkbox', function() {
        updateBookingSelectAllState();
        updateBulkBookingActionsVisibility();
    });

    // Handle bulk cancel button
    $('#bulk-cancel-btn').on('click', function() {
        const selectedIds = getSelectedBookingIds();
        if (selectedIds.length > 0) {
            bulkCancelBookings(selectedIds);
        }
    });

    // Handle bulk status change
    $('#bulk-status').on('change', function() {
        const status = $(this).val();
        if (status) {
            const selectedIds = getSelectedEventIds();
            if (selectedIds.length > 0) {
                bulkUpdateStatus(selectedIds, status);
            }
        }
    });

    // Fix dropdown positioning issues
    $(document).on('show.bs.dropdown', '.status-dropdown', function() {
        const $dropdown = $(this);
        const $menu = $dropdown.find('.dropdown-menu');
        
        // Ensure proper z-index
        $menu.css('z-index', '99999');
        
        // Move dropdown menu to body to avoid table overflow issues
        if ($menu.parent().is('body') === false) {
            $menu.appendTo('body');
        }
        
        // Get the position of the dropdown button
        const buttonOffset = $dropdown.offset();
        const buttonHeight = $dropdown.outerHeight();
        
        // Position the dropdown menu outside the table
        setTimeout(function() {
            $menu.css('position', 'fixed');
            $menu.css('top', (buttonOffset.top + buttonHeight + 2) + 'px');
            $menu.css('left', buttonOffset.left + 'px');
            $menu.css('width', $dropdown.outerWidth() + 'px');
        }, 10);
    });

    // Clean up dropdown menu when hidden
    $(document).on('hidden.bs.dropdown', '.status-dropdown', function() {
        const $dropdown = $(this);
        const $menu = $dropdown.find('.dropdown-menu');
        
        // Move the menu back to its original position
        if ($menu.parent().is('body')) {
            $dropdown.append($menu);
        }
    });

    // Handle bulk delete
    $('#bulk-delete-btn').on('click', function() {
        const selectedIds = getSelectedEventIds();
        if (selectedIds.length > 0) {
            bulkDeleteEvents(selectedIds);
        }
    });

    // Load bookings when bookings section is shown
    $(document).on('click', '#bookings-btn', function() {
        setTimeout(() => {
            if ($('#bookings-section').is(':visible')) {
                loadBookings();
            }
        }, 100);
    });

    // Also load bookings if the page loads with bookings section visible
    if ($('#bookings-section').is(':visible')) {
        loadBookings();
    }
});

// Bookings Helper Functions
function showCustomFieldsModal(customFieldsData, contactName) {
    let content = '<div class="row">';

    // Add contact name header
    content += `
        <div class="col-12 mb-4">
            <div class="alert alert-primary">
                <h6 class="mb-0">
                    <i class="ti ti-user me-2"></i>Custom Fields for: <strong>${contactName}</strong>
                </h6>
            </div>
        </div>
    `;

    if (customFieldsData && Array.isArray(customFieldsData) && customFieldsData.length > 0) {
        customFieldsData.forEach(function(field) {
            const icon = getFieldIcon(field.type);
            const formattedValue = formatFieldValue(field.type, field.value);

            content += `
                <div class="col-md-6 mb-3">
                    <div class="card custom-field-card h-100 border-primary">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-start">
                                <div class="flex-shrink-0 me-3">
                                    <div class="custom-field-icon bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="ti ti-${icon}"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-2 text-primary fw-bold">${field.label}</h6>
                                    <div class="field-value-container">
                                        <span class="badge bg-light text-dark fs-6 p-2 w-100 text-start">${formattedValue}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    } else {
        content += '<div class="col-12"><div class="alert alert-warning text-center"><i class="ti ti-info-circle me-2"></i>No custom fields available for this booking</div></div>';
    }

    content += '</div>';

    $('#customFieldsContent').html(content);
    $('#customFieldsModal').modal('show');
}

function getFieldLabel(fieldKey) {
    const fieldLabels = {
        'contact_type': 'Contact Type',
        'date_of_birth': 'Date of Birth',
        'business_type': 'Business Type',
        'business_gst_number': 'Business GST Number',
        'lead_value': 'Lead Value',
        'assigned_to_staff': 'Assigned to Staff',
        'contact_source': 'Contact Source',
        'opportunity_name': 'Opportunity Name',
        'postal_code': 'Postal Code',
        'full_name': 'Full Name',
        'specific_requirement': 'Any Specific Requirement',
        'used_whatsapp_api_chatbots': 'Have you used WhatsApp API and Chatbots ever',
        'generate_leads': 'How do you generate leads',
        'hear_about_omx_sales': 'Where did you hear about OMX Sales?',
        'city': 'City',
        'have_msme_certificate': 'Do you have MSME Certificate?',
        'whatsapp_number': 'WhatsApp Number',
        'meta_business_name': 'META Business Name',
        'have_website': 'Do you have a website?',
        'business_industry': 'Business Industry',
        'message': 'Message',
        'organization_task': 'Organization Task',
        'team_size': 'Team Size',
        'company_revenue': 'Company Revenue',
        'budget': 'What is your budget?',
        'real_estate_services': 'What type of real estate services do you offer?',
        'using_chatbot_tools': 'Are you currently using any chatbot or automation tools?',
        'implement_chatbot_timeframe': 'How soon are you looking to implement a chatbot?',
        'running_digital_ads': 'Are you currently running any digital ads?',
        'monthly_advertising_budget': 'Monthly Advertising Budget',
        'promoted_projects_count': 'How many real estate projects are you promoting?',
        'biggest_marketing_challenge': 'What is your biggest marketing challenge?',
        'property_price_range': 'What is the price range of the property you are selling?',
        'using_crm_software': 'Are you currently using any CRM software?',
        'advertising_on_third_party_platforms': 'Are you advertising on any third-party platforms?',
        'know_whatsapp_api': 'Do you know about the WhatsApp API?',
        'messages_volume': 'How many messages do you need to send?',
        'using_whatsapp_official_api': 'How are you currently doing WhatsApp Official API?',
        'monthly_lead_sales_volume': 'Monthly Lead/Sales Volume'
    };

    return fieldLabels[fieldKey] || fieldKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function formatFieldValue(fieldKey, value) {
    // Format specific field types
    if (fieldKey === 'date_of_birth' && value) {
        const date = new Date(value);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    if ((fieldKey.includes('budget') || fieldKey.includes('revenue') || fieldKey.includes('value')) && value) {
        // Format currency values
        if (!isNaN(value)) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(value);
        }
    }

    if (fieldKey === 'team_size' && value) {
        return value + ' employees';
    }

    if ((fieldKey.includes('have_') || fieldKey.includes('using_') || fieldKey.includes('know_')) && value) {
        return value.charAt(0).toUpperCase() + value.slice(1);
    }

    return value || 'Not specified';
}

function getFieldIcon(fieldKey) {
    const fieldIcons = {
        'contact_type': 'phone',
        'date_of_birth': 'calendar',
        'business_type': 'building-store',
        'business_gst_number': 'file-text',
        'lead_value': 'currency-dollar',
        'assigned_to_staff': 'user',
        'contact_source': 'source',
        'opportunity_name': 'target',
        'postal_code': 'map-pin',
        'full_name': 'user-circle',
        'specific_requirement': 'list-details',
        'used_whatsapp_api_chatbots': 'brand-whatsapp',
        'generate_leads': 'trending-up',
        'hear_about_omx_sales': 'ear',
        'city': 'map-pin',
        'have_msme_certificate': 'certificate',
        'whatsapp_number': 'brand-whatsapp',
        'meta_business_name': 'brand-facebook',
        'have_website': 'world-www',
        'business_industry': 'building',
        'message': 'message',
        'organization_task': 'checklist',
        'team_size': 'users',
        'company_revenue': 'chart-line',
        'budget': 'wallet',
        'real_estate_services': 'home',
        'using_chatbot_tools': 'robot',
        'implement_chatbot_timeframe': 'clock',
        'running_digital_ads': 'ad',
        'monthly_advertising_budget': 'currency-dollar',
        'promoted_projects_count': 'hash',
        'biggest_marketing_challenge': 'alert-triangle',
        'property_price_range': 'home-dollar',
        'using_crm_software': 'database',
        'advertising_on_third_party_platforms': 'external-link',
        'know_whatsapp_api': 'brand-whatsapp',
        'messages_volume': 'message-circle',
        'using_whatsapp_official_api': 'api',
        'monthly_lead_sales_volume': 'chart-bar'
    };
    return fieldIcons[fieldKey] || 'info-circle';
}
// Alert system functions for bookings
function showBookingsAlert(message, type = 'success') {
    const alertContainer = $('#bookings-alert-container');
    const alertMessage = $('#bookings-alert-message');
    const alertText = $('#bookings-alert-text');
    // Set alert type and message
    alertMessage.removeClass('alert-success alert-danger alert-warning alert-info');
    alertMessage.addClass(`alert-${type}`);
    alertText.text(message);

    // Show alert
    alertContainer.show();

    // Auto-hide after 5 seconds
    setTimeout(function() {
        alertContainer.fadeOut();
    }, 5000);
}

// Admin CRUD Functions for Bookings
// View booking details
function viewBooking(bookingId) {
    $.ajax({
        url: `{{ url('bookings') }}/${bookingId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const booking = response.data;
                const customFieldDetails = response.custom_field_details || {};
                
                let content = `
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <h6 class="card-title text-primary"><i class="ti ti-user me-2"></i>Customer Information</h6>
                                    <p><strong>Name:</strong> ${booking.name}</p>
                                    <p><strong>Email:</strong> ${booking.email}</p>
                                    <p><strong>Phone:</strong> ${booking.phone || 'N/A'}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h6 class="card-title text-info"><i class="ti ti-calendar me-2"></i>Booking Information</h6>
                                    <p><strong>Event:</strong> ${booking.event ? booking.event.title : 'N/A'}</p>
                                    <p><strong>Date:</strong> ${booking.date}</p>
                                    <p><strong>Time:</strong> ${booking.time || 'N/A'}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Add location information if available
                if (booking.selected_location) {
                    const location = booking.selected_location;
                    let locationDisplay = '';
                    let isClickable = false;
                    let clickAction = '';

                    if (location.type && location.value) {
                        switch(location.type) {
                            case 'zoom':
                                locationDisplay = `<i class="fas fa-video location-icon text-primary"></i>Zoom Meeting`;
                                isClickable = true;
                                clickAction = `onclick="window.open('${location.value}', '_blank')"`;
                                break;
                            case 'meet':
                                locationDisplay = `<i class="fab fa-google location-icon text-success"></i>Google Meet`;
                                isClickable = true;
                                clickAction = `onclick="window.open('${location.value}', '_blank')"`;
                                break;
                            case 'skype':
                                locationDisplay = `<i class="fab fa-skype location-icon text-info"></i>Skype`;
                                isClickable = true;
                                clickAction = `onclick="window.open('${location.value}', '_blank')"`;
                                break;
                            case 'phone':
                                locationDisplay = `<i class="fas fa-phone location-icon text-warning"></i>Phone: ${location.value}`;
                                isClickable = true;
                                clickAction = `onclick="window.open('tel:${location.value}', '_self')"`;
                                break;
                            case 'address':
                                locationDisplay = `<i class="fas fa-map-marker-alt location-icon text-danger"></i>${location.value}`;
                                isClickable = true;
                                clickAction = `onclick="window.open('https://maps.google.com/?q=${encodeURIComponent(location.value)}', '_blank')"`;
                                break;
                            case 'custom':
                                locationDisplay = `<i class="fas fa-map-marker-alt location-icon text-secondary"></i>${location.value}`;
                                break;
                            default:
                                locationDisplay = `<i class="fas fa-map-marker-alt location-icon text-secondary"></i>${location.display || location.value || 'Location specified'}`;
                        }
                    } else if (location.display) {
                        locationDisplay = `<i class="fas fa-map-marker-alt me-2 text-secondary"></i>${location.display}`;
                    }

                    if (locationDisplay) {
                        const cursorStyle = isClickable ? 'cursor: pointer;' : '';
                        const hoverStyle = isClickable ? 'text-decoration: underline;' : '';

                        content += `
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <div class="card border-warning">
                                        <div class="card-body">
                                            <h6 class="card-title text-warning"><i class="ti ti-map-pin me-2"></i>Meeting Location</h6>
                                            <p class="mb-0">
                                                <span class="${isClickable ? 'clickable-location' : ''}"
                                                      ${isClickable ? clickAction : ''}>
                                                    ${locationDisplay}
                                                </span>
                                                ${isClickable ? '<span class="location-hint">(Click to open)</span>' : ''}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                }

                // Add assigned staff information if available
                if (booking.event && booking.event.assigned_staff) {
                    const staff = booking.event.assigned_staff;
                    const staffType = staff.type.charAt(0).toUpperCase() + staff.type.slice(1);

                    content += `
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="card border-primary">
                                    <div class="card-body">
                                        <h6 class="card-title text-primary"><i class="ti ti-user-check me-2"></i>Assigned Staff</h6>
                                        <p class="mb-0">
                                            <span class="badge bg-primary me-2">
                                                <i class="ti ti-user me-1"></i>${staff.name}
                                            </span>
                                            <small class="text-muted">${staffType}</small>
                                        </p>
                                        ${staff.email ? `<small class="text-muted d-block mt-1"><i class="ti ti-mail me-1"></i>${staff.email}</small>` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    content += `
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="card border-light">
                                    <div class="card-body">
                                        <h6 class="card-title text-muted"><i class="ti ti-user-off me-2"></i>Assigned Staff</h6>
                                        <p class="mb-0">
                                            <span class="badge bg-light text-muted">
                                                <i class="ti ti-user-off me-1"></i>Not Assigned
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }

                // Add custom fields if available
                if (booking.custom_fields && booking.custom_fields_value) {
                    content += `
                        <div class="row">
                            <div class="col-12">
                                <div class="card border-success">
                                    <div class="card-body">
                                        <h6 class="card-title text-success"><i class="ti ti-forms me-2"></i>Custom Fields</h6>
                                        <div class="row">
                    `;

                    // Process custom fields - custom_fields contains field IDs, custom_fields_value contains values
                    const fieldIds = Array.isArray(booking.custom_fields) ? booking.custom_fields : [];
                    const fieldValues = Array.isArray(booking.custom_fields_value) ? booking.custom_fields_value : [];

                    // Create a mapping of field IDs to values
                    const fieldData = {};
                    for(let i = 0; i < Math.min(fieldIds.length, fieldValues.length); i++) {
                        fieldData[fieldIds[i]] = fieldValues[i];
                    }

                    // Display each custom field with proper label and value
                    Object.keys(fieldData).forEach(function(fieldId) {
                        const fieldValue = fieldData[fieldId];
                        // Use custom field details if available, otherwise fallback to generic label
                        const fieldDetail = customFieldDetails[fieldId];
                        const fieldLabel = fieldDetail ? fieldDetail.name : `Custom Field ${fieldId}`;
                        const formattedValue = fieldValue || 'N/A';

                        content += `
                            <div class="col-md-6 mb-2">
                                <strong>${fieldLabel}:</strong> ${formattedValue}
                            </div>
                        `;
                    });

                    content += `
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }

                content += `
                    <div class="row">
                        <div class="col-12">
                            <div class="card border-secondary">
                                <div class="card-body">
                                    <h6 class="card-title text-secondary"><i class="ti ti-info-circle me-2"></i>System Information</h6>
                                    <p><strong>Booking ID:</strong> ${booking.id}</p>
                                    <p><strong>Created:</strong> ${new Date(booking.created_at).toLocaleString()}</p>
                                    <p><strong>Updated:</strong> ${new Date(booking.updated_at).toLocaleString()}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                $('#viewBookingContent').html(content);
                $('#viewBookingModal').modal('show');
            } else {
                showBookingsAlert('Error loading booking details: ' + response.message, 'danger');
            }
        },
        error: function(xhr) {
            showBookingsAlert('Error loading booking details', 'danger');
            console.error(xhr.responseText);
        }
    });
}

// Edit booking
function editBooking(bookingId) {
    // Load booking data
    $.ajax({
        url: `{{ url('bookings') }}/${bookingId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const booking = response.data;
                const customFieldDetails = response.custom_field_details || {};

                // Populate form fields
                $('#edit_booking_id').val(booking.id);
                $('#edit_booking_name').val(booking.name);
                $('#edit_booking_email').val(booking.email);
                $('#edit_booking_phone').val(booking.phone || '');
                $('#edit_booking_date').val(booking.date);
                $('#edit_booking_time').val(booking.time || '');

                // Load events for dropdown
                loadEventsForEdit(booking.event_id);

                // Load custom fields with proper names
                loadCustomFieldsForEdit(booking, customFieldDetails);

                $('#editBookingModal').modal('show');
            } else {
                showBookingsAlert('Error loading booking details: ' + response.message, 'danger');
            }
        },
        error: function(xhr) {
            showBookingsAlert('Error loading booking details', 'danger');
            console.error(xhr.responseText);
        }
    });
}

// Load events for edit dropdown
function loadEventsForEdit(selectedEventId) {
    $.ajax({
        url: '{{ route("calendar-events.index") }}',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                let options = '<option value="">{{ __("Select Event") }}</option>';
                response.data.forEach(function(event) {
                    const selected = event.id == selectedEventId ? 'selected' : '';
                    options += `<option value="${event.id}" ${selected}>${event.title}</option>`;
                });
                $('#edit_booking_event').html(options);
            }
        },
        error: function(xhr) {
            console.error('Error loading events:', xhr.responseText);
        }
    });
}

// Load custom fields for booking module from CustomField model
function loadBookingCustomFields() {
    console.log('Loading booking custom fields...');
    
    $.ajax({
        url: '{{ route("calendar-events.booking-custom-fields") }}',
        method: 'GET',
        success: function(response) {
            console.log('Custom fields response:', response);
            
            if (response.success && response.data) {
                const customFields = response.data;
                let options = '<option value="">{{ __("No Custom Field") }}</option>';
                
                customFields.forEach(function(field) {
                    options += `<option value="${field.id}">${field.name}</option>`;
                });
                
                // Update the custom field dropdown
                $('#custom_field').html(options);
                
                console.log('Custom fields loaded:', customFields.length, 'fields');
            } else {
                console.log('No custom fields found or error loading fields');
                $('#custom_field').html('<option value="">{{ __("No Custom Field") }}</option>');
            }
        },
        error: function(xhr) {
            console.error('Error loading custom fields:', xhr.responseText);
            $('#custom_field').html('<option value="">{{ __("No Custom Field") }}</option>');
        }
    });
}

// Load custom fields for edit
function loadCustomFieldsForEdit(booking, customFieldDetails) {
    let customFieldsHtml = '';

    if (booking.custom_fields && booking.custom_fields_value) {
        const fieldIds = Array.isArray(booking.custom_fields) ? booking.custom_fields : [];
        const fieldValues = Array.isArray(booking.custom_fields_value) ? booking.custom_fields_value : [];

        if (fieldIds.length > 0) {
            customFieldsHtml += '<h6 class="mb-3"><i class="ti ti-forms me-2"></i>{{ __("Custom Fields") }}</h6>';

            // Create a mapping of field IDs to values
            const fieldData = {};
            for(let i = 0; i < Math.min(fieldIds.length, fieldValues.length); i++) {
                fieldData[fieldIds[i]] = fieldValues[i];
            }

            // Display each custom field for editing
            Object.keys(fieldData).forEach(function(fieldId, index) {
                const fieldValue = fieldData[fieldId];
                const fieldLabel = customFieldDetails[fieldId] ? customFieldDetails[fieldId].name : `Custom Field ${fieldId}`;

                customFieldsHtml += `
                    <div class="mb-3">
                        <label for="edit_custom_field_${index}" class="form-label">${fieldLabel}</label>
                        <input type="text" class="form-control" id="edit_custom_field_${index}"
                               name="custom_fields_value[${index}]" value="${fieldValue || ''}"
                               data-field-id="${fieldId}">
                    </div>
                `;
            });
        }
    }

    $('#edit_custom_fields_container').html(customFieldsHtml);
}

// Delete booking
function deleteBooking(bookingId) {
    // Create a more user-friendly confirmation dialog
    const confirmDelete = confirm('{{ __("Are you sure you want to delete this booking?") }}\n\n{{ __("This action cannot be undone and will permanently remove all booking data.") }}');

    if (confirmDelete) {
        $.ajax({
            url: `{{ url('bookings') }}/${bookingId}`,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    showBookingsAlert('{{ __("Booking deleted successfully") }}', 'success');
                    loadBookings(); // Refresh the bookings list
                } else {
                    showBookingsAlert('Error: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                showBookingsAlert('Error deleting booking', 'danger');
                console.error(xhr.responseText);
            }
        });
    }
}

// Handle edit booking form submission
$(document).on('submit', '#editBookingForm', function(e) {
    e.preventDefault();

    const bookingId = $('#edit_booking_id').val();
    const formData = {
        name: $('#edit_booking_name').val(),
        email: $('#edit_booking_email').val(),
        phone: $('#edit_booking_phone').val(),
        date: $('#edit_booking_date').val(),
        time: $('#edit_booking_time').val(),
        custom_fields_value: {}
    };

    // Collect custom fields values with their field IDs
    $('#edit_custom_fields_container input[name^="custom_fields_value"]').each(function() {
        const fieldId = $(this).data('field-id');
        const fieldValue = $(this).val();
        if (fieldId) {
            formData.custom_fields_value[fieldId] = fieldValue;
        }
    });

    $.ajax({
        url: `{{ url('bookings') }}/${bookingId}`,
        method: 'PUT',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: formData,
        success: function(response) {
            if (response.success) {
                showBookingsAlert('{{ __("Booking updated successfully") }}', 'success');
                $('#editBookingModal').modal('hide');
                loadBookings(); // Refresh the bookings list
            } else {
                showBookingsAlert('Error: ' + response.message, 'danger');
            }
        },
        error: function(xhr) {
            showBookingsAlert('Error updating booking', 'danger');
            console.error(xhr.responseText);
        }
    });
});



</script>

<!-- Custom Fields Modal -->
<div class="modal fade" id="customFieldsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="ti ti-forms me-2"></i>{{ __('Custom Fields Details') }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-4" id="customFieldsContent">
                <!-- Custom fields will be displayed here -->
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="ti ti-x me-1"></i>{{ __('Close') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- View Booking Modal -->
@can('manage booking')
<div class="modal fade" id="viewBookingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="ti ti-eye me-2"></i>{{ __('Appointment Details') }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="viewBookingContent">
                <!-- Booking details will be displayed here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="ti ti-x me-1"></i>{{ __('Close') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Booking Modal -->
<div class="modal fade" id="editBookingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title">
                    <i class="ti ti-edit me-2"></i>{{ __('Edit Booking') }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="editBookingForm">
                <div class="modal-body">
                    <input type="hidden" id="edit_booking_id">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_name" class="form-label">{{ __('Customer Name') }} *</label>
                            <input type="text" class="form-control" id="edit_booking_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_email" class="form-label">{{ __('Email') }} *</label>
                            <input type="email" class="form-control" id="edit_booking_email" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_phone" class="form-label">{{ __('Phone') }}</label>
                            <input type="text" class="form-control" id="edit_booking_phone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_event" class="form-label">{{ __('Event') }}</label>
                            <select class="form-control" id="edit_booking_event" disabled>
                                <option value="">{{ __('Loading events...') }}</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_date" class="form-label">{{ __('Date') }} *</label>
                            <input type="date" class="form-control" id="edit_booking_date" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_time" class="form-label">{{ __('Time') }}</label>
                            <input type="time" class="form-control" id="edit_booking_time">
                        </div>
                    </div>

                    <div id="edit_custom_fields_container">
                        <!-- Custom fields will be populated here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="ti ti-x me-1"></i>{{ __('Cancel') }}
                    </button>
                    <button type="submit" class="btn btn-warning">
                        <i class="ti ti-device-floppy me-1"></i>{{ __('Update Booking') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@endif

<!-- Location Modals -->
<!-- Zoom Modal -->
<div class="modal fade" id="zoomLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-video me-2 text-primary"></i>{{ __('Edit Location') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-3">
                        <i class="ti ti-video me-2 text-primary"></i>
                        <select class="form-select" id="zoom_type">
                            <option value="zoom">{{ __('Zoom') }}</option>
                        </select>
                    </div>
                    <input type="url" class="form-control" id="zoom_link" placeholder="https://zoom.us/j/123456789">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('CANCEL') }}</button>
                <button type="button" class="btn btn-success" onclick="saveLocation('zoom')">{{ __('UPDATE') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- In-Person Modal -->
<div class="modal fade" id="in_personLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-map-pin me-2 text-success"></i>{{ __('Edit Location') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-3">
                        <i class="ti ti-map-pin me-2 text-success"></i>
                        <select class="form-select" id="in_person_type">
                            <option value="in_person">{{ __('In-person meeting') }}</option>
                        </select>
                    </div>
                    <textarea class="form-control" id="in_person_address" rows="3" placeholder="{{ __('Enter the physical address or location details') }}"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('CANCEL') }}</button>
                <button type="button" class="btn btn-success" onclick="saveLocation('in_person')">{{ __('UPDATE') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Phone Modal -->
<div class="modal fade" id="phoneLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-phone me-2 text-info"></i>{{ __('Edit Location') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-3">
                        <i class="ti ti-phone me-2 text-info"></i>
                        <select class="form-select" id="phone_type">
                            <option value="phone">{{ __('Phone call') }}</option>
                        </select>
                    </div>
                    <input type="tel" class="form-control" id="phone_number" placeholder="{{ __('Enter phone number or call details') }}">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('CANCEL') }}</button>
                <button type="button" class="btn btn-success" onclick="saveLocation('phone')">{{ __('UPDATE') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Google Meet Modal -->
<div class="modal fade" id="meetLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-brand-google me-2 text-warning"></i>{{ __('Edit Location') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-3">
                        <i class="ti ti-brand-google me-2 text-warning"></i>
                        <select class="form-select" id="meet_type">
                            <option value="meet">{{ __('Google Meet') }}</option>
                        </select>
                    </div>
                    <input type="url" class="form-control" id="meet_link" placeholder="https://meet.google.com/abc-defg-hij">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('CANCEL') }}</button>
                <button type="button" class="btn btn-success" onclick="saveLocation('meet')">{{ __('UPDATE') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Skype Modal -->
<div class="modal fade" id="skypeLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-brand-skype me-2 text-primary"></i>{{ __('Edit Location') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-3">
                        <i class="ti ti-brand-skype me-2 text-primary"></i>
                        <select class="form-select" id="skype_type">
                            <option value="skype">{{ __('Skype') }}</option>
                        </select>
                    </div>
                    <input type="text" class="form-control" id="skype_link" placeholder="{{ __('Skype ID or meeting link') }}">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('CANCEL') }}</button>
                <button type="button" class="btn btn-success" onclick="saveLocation('skype')">{{ __('UPDATE') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Others Modal -->
<div class="modal fade" id="othersLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-dots me-2 text-secondary"></i>{{ __('Edit Location') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-3">
                        <i class="ti ti-dots me-2 text-secondary"></i>
                        <select class="form-select" id="others_type">
                            <option value="others">{{ __('Others') }}</option>
                        </select>
                    </div>
                    <textarea class="form-control" id="others_details" rows="3" placeholder="{{ __('Enter custom location details') }}"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('CANCEL') }}</button>
                <button type="button" class="btn btn-success" onclick="saveLocation('others')">{{ __('UPDATE') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Summernote JS -->
<script src="{{ asset('css/summernote/summernote-bs4.js') }}"></script>

<!-- Remaining Payment Link Modal -->
<div class="modal fade" id="remainingPaymentLinkModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="ti ti-link me-2"></i>{{ __('Remaining Payment Link') }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="ti ti-info-circle me-2"></i>
                    {{ __('Share this link with the customer to complete their remaining payment.') }}
                </div>
                <div class="mb-3">
                    <label for="remaining-payment-link" class="form-label">{{ __('Payment Link') }}</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="remaining-payment-link" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('remaining-payment-link')">
                            <i class="ti ti-copy me-1"></i>{{ __('Copy') }}
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="ti ti-x me-1"></i>{{ __('Close') }}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize Summernote for description field
    $('.summernote-simple').summernote({
        height: 200,
        toolbar: [
            ['style', ['bold', 'italic', 'underline', 'clear']],
            ['font', ['strikethrough', 'superscript', 'subscript']],
            ['fontsize', ['fontsize']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['insert', ['link']],
            ['view', ['fullscreen', 'codeview']]
        ],
        callbacks: {
            onPaste: function (e) {
                var bufferText = ((e.originalEvent || e).clipboardData || window.clipboardData).getData('Text');
                e.preventDefault();
                document.execCommand('insertText', false, bufferText);
            }
        }
    });
});

// Remaining Payment Functions
function generateRemainingPaymentLink(bookingId) {
    console.log('Generating remaining payment link for booking:', bookingId);
    
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Generating...';
    
    // Make AJAX call to generate link
    $.ajax({
        url: '{{ url("booking-remaining-payments") }}/' + bookingId + '/generate-link',
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Accept': 'application/json'
        },
        success: function(response) {
            if (response.success) {
                const link = response.data.remaining_payment_url;
                
                // Copy link to clipboard directly
                copyToClipboardDirectly(link);
                
                // Change button text to show "Copied"
                button.innerHTML = '<i class="ti ti-check me-1"></i>Copied';
                button.classList.remove('btn-outline-primary');
                button.classList.add('btn-success');
                
                // Reset button after 3 seconds
                setTimeout(function() {
                    button.disabled = false;
                    button.innerHTML = originalText;
                    button.classList.remove('btn-success');
                    button.classList.add('btn-outline-primary');
                }, 3000);
                
                // Show success message
                if (typeof showAlert !== 'undefined') {
                    showAlert('Payment link copied to clipboard!', 'success');
                } else {
                    alert('Payment link copied to clipboard!');
                }
            } else {
                alert('Error generating payment link: ' + (response.message || 'Unknown error'));
                // Reset button state on error
                button.disabled = false;
                button.innerHTML = originalText;
            }
        },
        error: function(xhr) {
            console.error('Error generating payment link:', xhr);
            let errorMessage = 'Error generating payment link.';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            alert(errorMessage);
            // Reset button state on error
            button.disabled = false;
            button.innerHTML = originalText;
        }
    });
}

// Direct copy to clipboard function
function copyToClipboardDirectly(text) {
    // Create a temporary input element
    const tempInput = document.createElement('input');
    tempInput.value = text;
    document.body.appendChild(tempInput);
    
    // Select and copy the text
    tempInput.select();
    tempInput.setSelectionRange(0, 99999); // For mobile devices
    
    try {
        document.execCommand('copy');
    } catch (err) {
        // Fallback for modern browsers
        navigator.clipboard.writeText(text).catch(function() {
            console.error('Failed to copy text');
        });
    }
    
    // Remove the temporary input
    document.body.removeChild(tempInput);
}

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999); // For mobile devices
    
    try {
        document.execCommand('copy');
        
        // Show success message
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="ti ti-check me-1"></i>Copied!';
        button.classList.remove('btn-outline-secondary');
        button.classList.add('btn-success');
        
        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    } catch (err) {
        console.error('Failed to copy: ', err);
        alert('Failed to copy to clipboard. Please copy manually.');
    }
}
</script>
@endpush