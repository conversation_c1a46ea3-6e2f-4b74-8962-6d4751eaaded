[2025-08-04 10:59:18] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-08-04T10:59:18.622349Z","source":"crm_webhook_system","action":"booking.event_created","user_id":74,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-08-04 10:59:21] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2047 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-04T10:59:21.747075Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":3101.0,"user_id":74,"entity_id":9,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-08-04T10:59:18.646415Z","data":{"id":9,"title":"Badminton","start_date":"2025-08-04 10:59:14","end_date":"2026-08-04 10:59:14","duration":60,"booking_per_slot":1,"minimum_notice":60,"description":"dewfefrewferwfre","location":"in_person","meet_link":null,"physical_address":"Badminton Court","custom_fields":[{"id":"11","name":"Gender","placeholder":"Enter gender","required":"false","type":"text","options":[]}],"date_override":null,"created_by":74,"slots_created":1480,"triggered_by":{"user_id":74,"email":"<EMAIL>","name":"deep sha","type":"company"}},"user_id":74,"triggered_by":{"user_id":74,"email":"<EMAIL>","name":"deep sha","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2047 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-04 10:59:21] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 1 {"timestamp":"2025-08-04T10:59:21.748557Z","source":"crm_webhook_system","action":"booking.event_created","user_id":74,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2047 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-08-04 11:09:27] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-08-04T11:09:27.574127Z","source":"crm_webhook_system","action":"booking.event_created","user_id":74,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-08-04 11:09:29] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2035 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-08-04T11:09:29.631553Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2055.0,"user_id":74,"entity_id":10,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-08-04T11:09:27.576885Z","data":{"id":10,"title":"Badminton","start_date":"2025-08-04 11:09:25","end_date":"2026-08-04 11:09:25","duration":60,"booking_per_slot":1,"minimum_notice":30,"description":null,"location":null,"meet_link":null,"physical_address":null,"custom_fields":[{"id":"11","name":"Gender","placeholder":"Enter gender","required":"false","type":"text","options":[]}],"date_override":null,"created_by":74,"slots_created":1480,"triggered_by":{"user_id":74,"email":"<EMAIL>","name":"deep sha","type":"company"}},"user_id":74,"triggered_by":{"user_id":74,"email":"<EMAIL>","name":"deep sha","type":"company"},"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2035 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-08-04 11:09:29] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 1 {"timestamp":"2025-08-04T11:09:29.632011Z","source":"crm_webhook_system","action":"booking.event_created","user_id":74,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2035 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
