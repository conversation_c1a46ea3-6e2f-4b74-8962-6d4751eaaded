<?php

namespace App\Http\Controllers;

use App\Models\CustomField;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Str;

class CustomFieldController extends Controller
{
    public function __construct()
    {

    }

    public function index()
    {
        if(Auth::user()->can('manage constant custom field'))
        {
            $custom_fields = CustomField::where('created_by', '=', Auth::user()->creatorId())->get();

            return view('customFields.index', compact('custom_fields'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }


    public function create()
    {
        if(\Auth::user()->can('create constant custom field'))
        {
            $types   = CustomField::$fieldTypes;
            $modules = CustomField::$modules;

            return view('customFields.create', compact('types', 'modules'));
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }


    public function store(Request $request)
    {
        if(Auth::user()->can('create constant custom field'))
        {
            $validator = Validator::make(
                $request->all(), [
                    'name' => 'required|max:40',
                    'type' => 'required',
                    'module' => 'required',
                ]
            );

            if($validator->fails())
            {
                if($request->ajax()) {
                    return response()->json([
                        'success' => false,
                        'errors' => $validator->errors()
                    ]);
                }
                return redirect()->back()->withErrors($validator)->withInput();
            }

            // Generate unique key
            $base_key = '{{'.Str::slug($request->module, '_').'.'.Str::slug($request->name, '_').'}}';
            $unique_key = $base_key;
            $counter = 1;

            while (CustomField::where('unique_key', $unique_key)->exists()) {
                $unique_key = $base_key . '_' . $counter++;
            }

            $custom_field = new CustomField();
            $custom_field->name = $request->name;
            $custom_field->type = $request->type;
            $custom_field->module = $request->module;
            $custom_field->created_by = Auth::user()->creatorId();
            $custom_field->is_required = $request->has('is_required') ? $request->is_required : false;
            $custom_field->status = $request->has('status') ? $request->status : true;
            $custom_field->unique_key = $unique_key;

            // Handle options for field types that require them
            if($request->has('options') && is_array($request->options)) {
                // Filter out empty values and store as JSON
                $filteredOptions = array_values(array_filter($request->options, function($option) {
                    return !empty(trim($option));
                }));
                
                // Store as JSON string in database
                $custom_field->options = !empty($filteredOptions) ? json_encode($filteredOptions) : null;
            } else {
                $custom_field->options = null;
            }

            $custom_field->save();

            if($request->ajax()) {
                // Get updated custom fields for the table
                $custom_fields = CustomField::where('created_by', Auth::user()->creatorId())->get();
                
                return response()->json([
                    'success' => true, 
                    'message' => __('Custom Field successfully created!'),
                    'data' => $custom_field,
                    'custom_fields' => $custom_fields
                ]);
            }

            return redirect()->route('settings.custom-fields')->with('success', __('Custom Field successfully created!'));
        }
        else
        {
            if($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => __('Permission Denied.')
                ], 403);
            }
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    public function getCustomFields()
    {
        if(Auth::user()->can('manage constant custom field'))
        {
            $custom_fields = CustomField::where('created_by', Auth::user()->creatorId())->get();
            
            return response()->json([
                'success' => true,
                'custom_fields' => $custom_fields
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => __('Permission Denied.')
        ], 403);
    }


    public function show(CustomField $customField)
    {
        return redirect()->route('settings');
    }

    public function edit(CustomField $customField)
    {
        if(Auth::user()->can('edit constant custom field'))
        {
            if($customField->created_by == Auth::user()->creatorId())
            {
                $types   = CustomField::$fieldTypes;
                $modules = CustomField::$modules;

                return view('customFields.edit', compact('customField', 'types', 'modules'));
            }
            else
            {
                return response()->json(['error' => __('Permission Denied.')], 401);
            }
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }


    public function update(Request $request, CustomField $customField)
    {
        if(Auth::user()->can('edit constant custom field'))
        {

            if($customField->created_by == Auth::user()->creatorId())
            {

                $validator = Validator::make(
                    $request->all(), [
                                       'name' => 'required|max:40',
                                   ]
                );

                if($validator->fails())
                {
                    $messages = $validator->getMessageBag();

                    if ($request->ajax()) {
                        return response()->json([
                            'success' => false,
                            'message' => $messages->first()
                        ]);
                    }

                    return redirect()->route('settings')->with('error', $messages->first());
                }

                $customField->name = $request->name;
                $customField->is_required = $request->has('is_required') ? $request->is_required : false;
                $customField->status = $request->has('status') ? $request->status : true;

                // Handle options for field types that require them
                if($request->has('options') && is_array($request->options)) {
                    // Filter out empty values and store as JSON
                    $filteredOptions = array_values(array_filter($request->options, function($option) {
                        return !empty(trim($option));
                    }));
                    
                    // Store as JSON string in database
                    $customField->options = !empty($filteredOptions) ? json_encode($filteredOptions) : null;
                } else {
                    $customField->options = null;
                }

                $customField->save();

                if ($request->ajax()) {
                    // Get updated custom fields for the table
                    $custom_fields = CustomField::where('created_by', Auth::user()->creatorId())->get();
                    
                    return response()->json([
                        'success' => true,
                        'message' => __('Custom Field successfully updated!'),
                        'custom_fields' => $custom_fields
                    ]);
                }

                return redirect()->route('settings')->with('success', __('Custom Field successfully updated!'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }


    public function toggleRequired($id)
    {
        $customField = CustomField::find($id);
        
        if (!$customField || $customField->created_by != Auth::user()->creatorId()) {
            return response()->json(['success' => false, 'message' => __('Permission Denied.')]);
        }

        $customField->is_required = !$customField->is_required;
        $customField->save();

        return response()->json(['success' => true, 'required' => $customField->is_required]);
    }

    public function toggleStatus(Request $request)
    {
        if (!Auth::user()->can('edit constant custom field')) {
            return response()->json(['success' => false, 'message' => __('Permission Denied.')]);
        }

        $fieldId = $request->input('field_id');
        $newStatus = $request->input('status');

        $customField = CustomField::find($fieldId);
        
        if (!$customField || $customField->created_by != Auth::user()->creatorId()) {
            return response()->json(['success' => false, 'message' => __('Permission Denied.')]);
        }

        $customField->status = $newStatus;
        $customField->save();

        $statusText = $newStatus ? __('Active') : __('Inactive');
        return response()->json([
            'success' => true, 
            'status' => $customField->status,
            'message' => __('Custom field status updated to :status', ['status' => $statusText])
        ]);
    }

    public function destroy(CustomField $customField)
    {
        if(Auth::user()->can('delete constant custom field'))
        {
            if($customField->created_by == Auth::user()->creatorId())
            {
                $customField->delete();

                if(request()->ajax()) {
                    // Get updated custom fields for the table
                    $custom_fields = CustomField::where('created_by', Auth::user()->creatorId())->get();
                    
                    return response()->json([
                        'success' => true,
                        'message' => __('Custom Field successfully deleted!'),
                        'custom_fields' => $custom_fields
                    ]);
                }

                return redirect()->route('settings')->with('success', __('Custom Field successfully deleted!'));
            }
            else
            {
                if(request()->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => __('Permission Denied.')
                    ], 403);
                }
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            if(request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => __('Permission Denied.')
                ], 403);
            }
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }
}
