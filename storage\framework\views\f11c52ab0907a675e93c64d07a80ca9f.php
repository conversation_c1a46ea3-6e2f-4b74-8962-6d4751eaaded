<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Copy Event</title>
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Select2 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
  <!-- FullCalendar CSS -->
  <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/main.min.css" rel="stylesheet">
  <!-- jQuery -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <!-- Select2 JS -->
  <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
  <!-- Moment.js with Timezone -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.43/moment-timezone-with-data.min.js"></script>
  <style>
    body {
      font-family: 'Inter', Arial, sans-serif;
      background: #fafbfc;
      color: #111827;
      min-height: 100vh;
    }
    .main-card {
      background: #fff;
      border-radius: 1.2rem;
      box-shadow: 0 4px 24px 0 rgba(16, 185, 129, 0.08);
      max-width: 1300px;
      margin: 2rem auto;
      padding: 2.5rem 2rem;
    }
    .event-title {
      font-size: 2.2rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      color: #111827;
    }
    .event-detail-icon {
      color: #22c55e;
      font-size: 1.5rem;
      margin-right: 0.5rem;
      vertical-align: middle;
    }
    .event-detail-label {
      font-weight: 600;
      color: #22c55e;
      margin-right: 0.5rem;
    }
    .event-description {
      color: #374151;
      font-size: 1.1rem;
      margin-top: 1.5rem;
    }
    .calendar-section {
      background: #f0fdf4;
      border-radius: 1rem;
      padding: 2rem 1.5rem 1.5rem 1.5rem;
      box-shadow: 0 2px 8px 0 rgba(16, 185, 129, 0.04);
    }
    .fc .fc-toolbar-title {
      color: #111827;
      font-weight: 700;
      font-size: 1.2rem;
    }
    .fc .fc-button {
      background: #2563eb;
      border: none;
      color: #fff;
      border-radius: 0.5rem;
      font-weight: 600;
      transition: background 0.2s;
    }
    .fc .fc-button:hover, .fc .fc-button:focus {
      background: #1d4ed8;
      color: #fff;
    }
    .fc .fc-daygrid-day-number {
      color: #2563eb;
      font-weight: 600;
    }
    .fc .fc-daygrid-day.fc-day-today {
      background: #dbeafe;
    }
    .timezone-label {
      font-weight: 600;
      margin-top: 1.5rem;
      margin-bottom: 0.5rem;
      color: #111827;
    }
    .form-select, .form-control {
      border-radius: 0.5rem;
      border: 1px solid #bbf7d0;
      font-size: 1rem;
      color: #111827;
      background: #fff;
    }
    .form-select:focus, .form-control:focus {
      border-color: #22c55e;
      box-shadow: 0 0 0 0.2rem rgba(34,197,94,.10);
    }
    .time-format-toggle {
      margin-left: 1rem;
      font-weight: 500;
      color: #111827;
    }
    .time-format-toggle input[type="radio"] {
      accent-color: #22c55e;
      margin-right: 0.25rem;
    }
    .booking-btn {
      background: #22c55e;
      color: #fff;
      border-radius: 0.5rem;
      font-weight: 600;
      padding: 0.75rem 2rem;
      border: none;
      margin-top: 2rem;
      transition: background 0.2s;
    }
    .booking-btn:disabled {
      background: #bbf7d0;
      color: #fff;
      opacity: 0.7;
    }
    .booking-btn:hover:not(:disabled) {
      background: #16a34a;
      color: #fff;
    }
    .btn-outline-primary, .btn-outline-primary:focus, .btn-outline-primary:active {
      color: #22c55e !important;
      border-color: #22c55e !important;
      background: #fff !important;
      font-weight: 600;
    }
    .btn-outline-primary:hover {
      background: #22c55e !important;
      color: #fff !important;
      border-color: #22c55e !important;
    }
    .bg-primary, .modal-header.bg-primary {
      background: #22c55e !important;
      color: #fff !important;
    }
    .text-bg-success {
      background: #22c55e !important;
      color: #fff !important;
    }
    .calendar-day-btn {
      background: #bbf7d0 !important;
      color: #16a34a !important;
      font-weight: 700;
      border: none;
    }
    .calendar-day-btn[style*='border:2px'] {
      border: 2px solid #22c55e !important;
    }
    .calendar-day-btn:focus {
      outline: 2px solid #22c55e;
    }
    .calendar-day-btn.active, .calendar-day-btn.selected {
      background: #22c55e !important;
      color: #fff !important;
    }
    .list-group-item-action.time-slot-btn {
      color: #16a34a;
      border: 1px solid #22c55e;
      background: #fff;
      font-weight: 600;
    }
    .list-group-item-action.time-slot-btn.active, .list-group-item-action.time-slot-btn:focus, .list-group-item-action.time-slot-btn:hover {
      background: #22c55e;
      color: #fff;
      border-color: #22c55e;
    }
    /* Disabled time slot styles - Red theme for booked slots */
    .time-slot-btn-disabled {
      background: #fef2f2 !important;
      color: #dc2626 !important;
      border: 2px solid #fca5a5 !important;
      cursor: not-allowed !important;
      font-weight: 600;
      position: relative;
    }
    .time-slot-btn-disabled:hover {
      background: #fef2f2 !important;
      color: #dc2626 !important;
      border-color: #fca5a5 !important;
      transform: none;
    }
    .time-slot-btn-disabled:before {
      content: "🚫";
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 12px;
    }
    /* Past time slot styles - Gray theme for past slots */
    .time-slot-btn-past {
      background: #f3f4f6 !important;
      color: #6b7280 !important;
      border: 2px solid #d1d5db !important;
      cursor: not-allowed !important;
      font-weight: 500;
      opacity: 0.7;
    }
    .time-slot-btn-past:hover {
      background: #f3f4f6 !important;
      color: #6b7280 !important;
      border-color: #d1d5db !important;
      transform: none;
    }
    @media (max-width: 991px) {
      .main-card {
        padding: 1.2rem 0.5rem;
      }
    }
    @media (max-width: 767px) {
      .main-card {
        padding: 0.5rem 0.2rem;
      }
      .calendar-section {
        padding: 1rem 0.5rem;
      }
      #customCalendar .d-flex, #customCalendar .flex-fill {
        width: 100% !important;
        min-width: 32px;
        max-width: 100%;
      }
      #customCalendar button.calendar-day-btn {
        width: 32px !important;
        height: 32px !important;
        font-size: 1rem;
      }
    }
    .calendar-grid-header, .calendar-grid-days {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 2px;
    }
    @media (max-width: 767px) {
      #customCalendar .calendar-grid-header, #customCalendar .calendar-grid-days {
        grid-template-columns: repeat(7, 1fr) !important;
      }
      #customCalendar button.calendar-day-btn {
        width: 32px !important;
        height: 32px !important;
        font-size: 1rem;
      }
    }

    /* Location Selection Styles */
    .location-options .form-check {
      border: 1px solid #e5e7eb;
      border-radius: 0.5rem;
      padding: 0.75rem;
      margin-bottom: 0.5rem;
      transition: all 0.2s ease;
      cursor: pointer;
    }

    .location-options .form-check:hover {
      border-color: #22c55e;
      background-color: #f0fdf4;
    }

    .location-options .form-check-input:checked + .form-check-label {
      color: #22c55e;
      font-weight: 600;
    }

    .location-options .form-check-input:checked {
      background-color: #22c55e;
      border-color: #22c55e;
    }

    .location-options .form-check:has(.form-check-input:checked) {
      border-color: #22c55e;
      background-color: #f0fdf4;
      box-shadow: 0 0 0 0.2rem rgba(34, 197, 94, 0.1);
    }

    .location-legacy .border {
      transition: all 0.2s ease;
    }

    .location-legacy .border:hover {
      border-color: #22c55e;
      box-shadow: 0 2px 8px rgba(34, 197, 94, 0.1);
    }

    /* Select2 Custom Styles */
    .select2-container--default .select2-selection--single {
      border-radius: 0.5rem;
      border: 1px solid #bbf7d0;
      height: 38px;
      padding: 5px;
    }

    .select2-container--default .select2-selection--single:focus {
      border-color: #22c55e;
      box-shadow: 0 0 0 0.2rem rgba(34,197,94,.10);
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
      background-color: #22c55e;
    }

    .select2-search__field:focus {
      border-color: #22c55e !important;
    }

    .select2-container--default .select2-search--dropdown .select2-search__field {
      border-radius: 0.25rem;
      border: 1px solid #bbf7d0;
    }

    .select2-results__option {
      padding: 8px 12px;
    }
    
    /* Date Range Information Styling */
    .booking-period-info {
      background: #f0fdf4;
      border: 1px solid #bbf7d0;
      border-radius: 0.5rem;
      padding: 0.75rem;
      margin-top: 1rem;
    }
    
    .booking-period-info .event-detail-label {
      color: #16a34a;
      font-weight: 600;
    }
  </style>
</head>
<body>
  <div class="main-card">
    <div class="row g-5 align-items-start">
      <!-- Left: Event Details -->
      <div class="col-lg-5 col-12 mb-4 mb-lg-0">
              <div>
          <div class="event-title"><?php echo e($event->title); ?></div>
          <div class="d-flex align-items-center mb-2">
            <span class="event-detail-icon"><svg xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='none' stroke='#2563eb' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-clock'><circle cx='12' cy='12' r='10'/><polyline points='12 6 12 12 16 14'/></svg></span>
            <span class="event-detail-label"><?php echo e($event->duration ?? 30); ?> min</span>
              </div>
          <div class="d-flex align-items-center mb-2">
            <span class="event-detail-icon"><svg xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='none' stroke='#2563eb' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-map-pin'><path d='M21 10c0 7-9 13-9 13S3 17 3 10a9 9 0 1 1 18 0z'/><circle cx='12' cy='10' r='3'/></svg></span>
            <span class="event-detail-label">
              <?php if($event->location === 'in_person'): ?>
                In-person meeting details will be provided upon confirmation.
              <?php elseif($event->location === 'zoom'): ?>
                Zoom meeting link will be shared after booking.
              <?php elseif($event->location === 'skype'): ?>
                Skype meeting link will be shared after booking.
              <?php elseif($event->location === 'meet'): ?>
                Google Meet link will be shared after booking.
              <?php else: ?>
                <?php echo e(ucfirst($event->location ?? 'Other')); ?>

              <?php endif; ?>
            </span>
          </div>
          <?php if($event->physical_address): ?>
            <div class="event-description"><?php echo e($event->physical_address); ?></div>
          <?php endif; ?>
          <?php if($event->description): ?>
            <div class="event-description"><?php echo $event->description; ?></div>
          <?php endif; ?>
          
          <!-- Date Range Information -->
          <?php if($event->date_range_type): ?>
            <div class="booking-period-info">
              <div class="d-flex align-items-center">
                <span class="event-detail-icon">
                  <svg xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='none' stroke='#16a34a' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-calendar'>
                    <rect x='3' y='4' width='18' height='18' rx='2' ry='2'/>
                    <line x1='16' y1='2' x2='16' y2='6'/>
                    <line x1='8' y1='2' x2='8' y2='6'/>
                    <line x1='3' y1='10' x2='21' y2='10'/>
                  </svg>
                </span>
                <span class="event-detail-label">
                  <?php if($event->date_range_type === 'calendar_days'): ?>
                    Available for <?php echo e($event->date_range_days ?? 30); ?> calendar days from today
                  <?php elseif($event->date_range_type === 'date_range'): ?>
                    <?php if($event->date_range_start && $event->date_range_end): ?>
                      Available from <?php echo e(\Carbon\Carbon::parse($event->date_range_start)->format('M j, Y')); ?> to <?php echo e(\Carbon\Carbon::parse($event->date_range_end)->format('M j, Y')); ?>

                    <?php else: ?>
                      Date range not specified
                    <?php endif; ?>
                  <?php elseif($event->date_range_type === 'indefinitely'): ?>
                    Available indefinitely
                  <?php endif; ?>
                </span>
              </div>
            </div>
          <?php endif; ?>
          
          <!-- Time Zone Section Moved Here -->
          <div class="timezone-label mt-4">Time Zone</div>
          <select class="form-select mb-2" id="timezoneSelect" data-placeholder="Search for your timezone..."></select>
          <script>
            $(document).ready(function() {
              // Timezone data with common names
              const timezones = moment.tz.names().map(tz => {
                const offset = moment.tz(tz).format('Z');
                const name = tz.replace(/_/g, ' ').replace(/\//g, ' / ');
                return {
                  id: tz,
                  text: `(GMT${offset}) ${name}`,
                  offset: offset
                };
              });

              // Initialize Select2
              $('#timezoneSelect').select2({
                data: timezones,
                placeholder: 'Search for your timezone...',
                allowClear: true,
                matcher: function(params, data) {
                  // If there are no search terms, return all of the data
                  if ($.trim(params.term) === '') {
                    return data;
                  }

                  // Do not display the item if there is no 'text' property
                  if (typeof data.text === 'undefined') {
                    return null;
                  }

                  // Search in both timezone name and offset
                  const searchStr = params.term.toLowerCase();
                  if (data.text.toLowerCase().indexOf(searchStr) > -1 ||
                      data.id.toLowerCase().replace(/_/g, ' ').indexOf(searchStr) > -1) {
                    return data;
                  }

                  // Return `null` if the term should not be displayed
                  return null;
                }
              });

              // Set default timezone (user's local timezone)
              const userTimezone = moment.tz.guess();
              $('#timezoneSelect').val(userTimezone).trigger('change');

              // Handle timezone change
              $('#timezoneSelect').on('change', function() {
                const selectedTimezone = $(this).val();
                // Refresh time slots if a date is selected
                const selectedDate = document.getElementById('selected_date').value;
                if (selectedDate) {
                  showTimeSlotsPanel(selectedDate);
                }
              });
            });
          </script>

          <!-- Location Selection Section -->
          <div class="timezone-label mt-4">Location</div>
          <div class="mb-3">
            <?php if($event->locations_data): ?>
              <?php
                $locations = json_decode($event->locations_data, true);
              ?>
              <?php if($locations && count($locations) > 0): ?>
                <div class="location-options">
                  <?php $__currentLoopData = $locations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="form-check mb-2">
                      <input class="form-check-input" type="radio" name="selected_location" id="location_<?php echo e($index); ?>" value="<?php echo e(json_encode($location)); ?>" <?php echo e($index === 0 ? 'checked' : ''); ?>>
                      <label class="form-check-label d-flex align-items-center" for="location_<?php echo e($index); ?>">
                        <?php if($location['type'] === 'zoom'): ?>
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#2563eb" class="me-2" viewBox="0 0 24 24">
                            <path d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2z"/>
                            <path d="M9 12l6-3v6l-6-3z"/>
                          </svg>
                          <span class="fw-medium"><?php echo e($location['display']); ?></span>
                          <small class="text-muted ms-2">Online meeting/conference</small>
                        <?php elseif($location['type'] === 'in_person'): ?>
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#22c55e" class="me-2" viewBox="0 0 24 24">
                            <path d="M21 10c0 7-9 13-9 13S3 17 3 10a9 9 0 1 1 18 0z"/>
                            <circle cx="12" cy="10" r="3"/>
                          </svg>
                          <span class="fw-medium"><?php echo e($location['display']); ?></span>
                          <small class="text-muted ms-2"><?php echo e($location['value'] ?: 'Address will be provided'); ?></small>
                        <?php elseif($location['type'] === 'phone'): ?>
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#06b6d4" class="me-2" viewBox="0 0 24 24">
                            <path d="M22 16.92v3a2 2 0 01-2.18 2 19.79 19.79 0 01-8.63-3.07 19.5 19.5 0 01-6-6 19.79 19.79 0 01-3.07-8.67A2 2 0 014.11 2h3a2 2 0 012 1.72 12.84 12.84 0 00.7 2.81 2 2 0 01-.45 2.11L8.09 9.91a16 16 0 006 6l1.27-1.27a2 2 0 012.11-.45 12.84 12.84 0 002.81.7A2 2 0 0122 16.92z"/>
                          </svg>
                          <span class="fw-medium"><?php echo e($location['display']); ?></span>
                          <small class="text-muted ms-2"><?php echo e($location['value'] ?: 'Phone details will be provided'); ?></small>
                        <?php elseif($location['type'] === 'meet'): ?>
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#f59e0b" class="me-2" viewBox="0 0 24 24">
                            <path d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2z"/>
                            <path d="M9 12l6-3v6l-6-3z"/>
                          </svg>
                          <span class="fw-medium"><?php echo e($location['display']); ?></span>
                          <small class="text-muted ms-2">Online meeting/conference</small>
                        <?php elseif($location['type'] === 'skype'): ?>
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#2563eb" class="me-2" viewBox="0 0 24 24">
                            <path d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2z"/>
                            <path d="M9 12l6-3v6l-6-3z"/>
                          </svg>
                          <span class="fw-medium"><?php echo e($location['display']); ?></span>
                          <small class="text-muted ms-2">Online meeting/conference</small>
                        <?php else: ?>
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#6b7280" class="me-2" viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="3"/>
                            <circle cx="12" cy="12" r="1"/>
                            <circle cx="12" cy="12" r="8"/>
                          </svg>
                          <span class="fw-medium"><?php echo e($location['display']); ?></span>
                          <small class="text-muted ms-2"><?php echo e($location['value'] ?: 'Details will be provided'); ?></small>
                        <?php endif; ?>
                      </label>
                    </div>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
              <?php else: ?>
                <div class="alert alert-info">
                  <i class="me-2">ℹ️</i>Location details will be provided after booking confirmation.
                </div>
              <?php endif; ?>
            <?php else: ?>
              <!-- Legacy single location display -->
              <div class="location-legacy">
                <?php if($event->location === 'in_person'): ?>
                  <div class="d-flex align-items-center p-3 border rounded bg-light">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="#22c55e" class="me-3" viewBox="0 0 24 24">
                      <path d="M21 10c0 7-9 13-9 13S3 17 3 10a9 9 0 1 1 18 0z"/>
                      <circle cx="12" cy="10" r="3"/>
                    </svg>
                    <div>
                      <div class="fw-medium">In-Person Meeting</div>
                      <small class="text-muted"><?php echo e($event->physical_address ?: 'Address will be provided upon confirmation'); ?></small>
                    </div>
                  </div>
                <?php elseif($event->location === 'zoom'): ?>
                  <div class="d-flex align-items-center p-3 border rounded bg-light">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="#2563eb" class="me-3" viewBox="0 0 24 24">
                      <path d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2z"/>
                      <path d="M9 12l6-3v6l-6-3z"/>
                    </svg>
                    <div>
                      <div class="fw-medium">Zoom Meeting</div>
                      <small class="text-muted">Meeting link will be shared after booking</small>
                    </div>
                  </div>
                <?php elseif($event->location === 'meet'): ?>
                  <div class="d-flex align-items-center p-3 border rounded bg-light">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="#f59e0b" class="me-3" viewBox="0 0 24 24">
                      <path d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2z"/>
                      <path d="M9 12l6-3v6l-6-3z"/>
                    </svg>
                    <div>
                      <div class="fw-medium">Google Meet</div>
                      <small class="text-muted">Meeting link will be shared after booking</small>
                    </div>
                  </div>
                <?php elseif($event->location === 'skype'): ?>
                  <div class="d-flex align-items-center p-3 border rounded bg-light">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="#2563eb" class="me-3" viewBox="0 0 24 24">
                      <path d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2z"/>
                      <path d="M9 12l6-3v6l-6-3z"/>
                    </svg>
                    <div>
                      <div class="fw-medium">Skype Meeting</div>
                      <small class="text-muted">Meeting link will be shared after booking</small>
                    </div>
                  </div>
                <?php else: ?>
                  <div class="d-flex align-items-center p-3 border rounded bg-light">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="#6b7280" class="me-3" viewBox="0 0 24 24">
                      <circle cx="12" cy="12" r="3"/>
                      <circle cx="12" cy="12" r="1"/>
                      <circle cx="12" cy="12" r="8"/>
                    </svg>
                    <div>
                      <div class="fw-medium"><?php echo e(ucfirst($event->location ?? 'Other')); ?></div>
                      <small class="text-muted">Location details will be provided upon confirmation</small>
                    </div>
                  </div>
                <?php endif; ?>
              </div>
            <?php endif; ?>
          </div>
              </div>
            </div>
      <!-- Right: Calendar and Booking -->
      <div class="col-lg-7 col-12">
        <div class="calendar-section row g-0">
          <div class="col-12" id="calendarCol">
            <div class="fw-bold fs-5 mb-2">Select a Date & Time</div>
            <div id="customCalendar"></div>
          </div>
        </div>
        <!-- Timezone section removed from here -->
        <div class="d-flex align-items-center mb-2">
          <span class="timezone-label mb-0">Time Format</span>
          <div class="time-format-toggle ms-3">
            <input type="radio" name="timeFormat" id="format12" value="12" checked> <label for="format12">12h</label>
            <input type="radio" name="timeFormat" id="format24" value="24" class="ms-2"> <label for="format24">24h</label>
          </div>
        </div>
        <div class="alert alert-success d-none" id="slotConfirmation">
          <strong>Selected:</strong> <span id="selectedDateTime"></span>
        </div>
        <input type="hidden" id="selected_date">
        <input type="hidden" id="selected_time">
        <button class="booking-btn w-100" id="bookBtn" disabled>Book This Slot</button>
    </div>
  </div>
</div>

  <!-- Booking Modal (for form) -->
<div class="modal fade" id="bookingFormModal" tabindex="-1">
    <div class="modal-dialog">
      <form id="finalBookingForm" class="modal-content">
        <div class="modal-header" style="background:#3C8764;color:#fff;">
          <h5 class="modal-title">Confirm Booking</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
            <strong>Event:</strong> <?php echo e($event->title); ?><br>
            <strong>Date & Time:</strong> <span id="confirm-slot-time"></span><br>
            <strong>Duration:</strong> <?php echo e($event->duration ?? 60); ?> minutes<br>
            <strong>Location:</strong> <span id="confirm-location-display">Will be updated based on selection</span>
            <?php if($event->payment_required && $event->payment_amount): ?>
              <br><strong>Payment Required:</strong> <span class="text-success"><?php echo e($event->payment_currency ?? 'USD'); ?> <?php echo e(number_format($event->payment_amount, 2)); ?></span>
              
              <?php if($event->partial_payment_enabled && $event->partial_payment_percentage): ?>
                <div class="mt-2">
                  <div class="form-check">
                    <input class="form-check-input" type="radio" id="full_payment_option" name="payment_type" value="full" checked>
                    <label class="form-check-label" for="full_payment_option">
                      <strong>Full Payment:</strong> <?php echo e($event->payment_currency ?? 'USD'); ?> <?php echo e(number_format($event->payment_amount, 2)); ?>

                    </label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="radio" id="partial_payment_option" name="payment_type" value="partial">
                    <label class="form-check-label" for="partial_payment_option">
                      <strong>Partial Payment:</strong> <?php echo e($event->payment_currency ?? 'USD'); ?> <?php echo e(number_format(($event->payment_amount * $event->partial_payment_percentage) / 100, 2)); ?> (<?php echo e($event->partial_payment_percentage); ?>%)
                    </label>
                  </div>
                  <small class="text-muted">Remaining <?php echo e($event->payment_currency ?? 'USD'); ?> <?php echo e(number_format($event->payment_amount - (($event->payment_amount * $event->partial_payment_percentage) / 100), 2)); ?> to be paid later</small>
                </div>
              <?php endif; ?>
            <?php endif; ?>
          </div>
          <div class="mb-3">
            <label for="attendee_name" class="form-label">Name <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="attendee_name" required>
          </div>
          <div class="mb-3">
            <label for="attendee_email" class="form-label">Email <span class="text-danger">*</span></label>
            <input type="email" class="form-control" id="attendee_email" required>
          </div>
          <div class="mb-3">
            <label for="attendee_phone" class="form-label">Phone</label>
            <input type="text" class="form-control" id="attendee_phone">
          </div>
          <!-- Custom Fields -->
          <?php
            // Debug: Log the custom fields data
            \Log::info('Custom fields data in copy_event view:', [
                'event_id' => $event->id,
                'custom_fields' => $event->custom_fields
            ]);
          ?>
       
          <?php if($event->custom_fields && count($event->custom_fields) > 0): ?>
            <?php $__currentLoopData = $event->custom_fields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              <div class="mb-3">
                <label for="custom_field_<?php echo e($index); ?>" class="form-label">
                  <?php echo e($field['name'] ?? 'Custom Field'); ?>

                  <?php if(isset($field['required']) && $field['required'] === 'true'): ?>
                    <span class="text-danger">*</span>
                  <?php endif; ?>
                </label>
                <?php
                  $placeholder = $field['placeholder'] ?? 'Enter ' . strtolower($field['name'] ?? 'value');
                  $isRequired = isset($field['required']) && $field['required'] === 'true' ? 'required' : '';
                ?>
                <?php if($field['type'] === 'date'): ?>
                  <input type="date" class="form-control" id="custom_field_<?php echo e($index); ?>" name="custom_fields[<?php echo e($field['id']); ?>]" placeholder="<?php echo e($placeholder); ?>" <?php echo e($isRequired); ?>>
                <?php elseif($field['type'] === 'select'): ?>
                  <select class="form-control" id="custom_field_<?php echo e($index); ?>" name="custom_fields[<?php echo e($field['id']); ?>]" <?php echo e($isRequired); ?>>
                    <option value=""><?php echo e($placeholder); ?></option>
                    <?php if(isset($field['options']) && is_array($field['options'])): ?>
                      <?php $__currentLoopData = $field['options']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($option); ?>"><?php echo e($option); ?></option>
                      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                  </select>
                <?php elseif($field['type'] === 'radio'): ?>
                  <?php if(isset($field['options']) && is_array($field['options'])): ?>
                    <?php $__currentLoopData = $field['options']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $optionIndex => $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <div class="form-check">
                        <input class="form-check-input" type="radio" name="custom_fields[<?php echo e($field['id']); ?>]" id="custom_field_<?php echo e($index); ?>_<?php echo e($optionIndex); ?>" value="<?php echo e($option); ?>" <?php echo e($isRequired); ?>>
                        <label class="form-check-label" for="custom_field_<?php echo e($index); ?>_<?php echo e($optionIndex); ?>">
                          <?php echo e($option); ?>

                        </label>
                      </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                  <?php endif; ?>
                <?php elseif($field['type'] === 'checkbox'): ?>
                  <?php if(isset($field['options']) && is_array($field['options'])): ?>
                    <?php $__currentLoopData = $field['options']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $optionIndex => $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="custom_fields[<?php echo e($field['id']); ?>][]" id="custom_field_<?php echo e($index); ?>_<?php echo e($optionIndex); ?>" value="<?php echo e($option); ?>" <?php echo e($isRequired); ?>>
                        <label class="form-check-label" for="custom_field_<?php echo e($index); ?>_<?php echo e($optionIndex); ?>">
                          <?php echo e($option); ?>

                        </label>
                      </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                  <?php endif; ?>
                <?php elseif($field['type'] === 'textarea'): ?>
                  <textarea class="form-control" id="custom_field_<?php echo e($index); ?>" name="custom_fields[<?php echo e($field['id']); ?>]" rows="3" placeholder="<?php echo e($placeholder); ?>" <?php echo e($isRequired); ?>></textarea>
                <?php elseif($field['type'] === 'number'): ?>
                  <input type="number" class="form-control" id="custom_field_<?php echo e($index); ?>" name="custom_fields[<?php echo e($field['id']); ?>]" placeholder="<?php echo e($placeholder); ?>" <?php echo e($isRequired); ?>>
                <?php elseif($field['type'] === 'email'): ?>
                  <input type="email" class="form-control" id="custom_field_<?php echo e($index); ?>" name="custom_fields[<?php echo e($field['id']); ?>]" placeholder="<?php echo e($placeholder); ?>" <?php echo e($isRequired); ?>>
                <?php else: ?>
                  <input type="text" class="form-control" id="custom_field_<?php echo e($index); ?>" name="custom_fields[<?php echo e($field['id']); ?>]" placeholder="<?php echo e($placeholder); ?>" <?php echo e($isRequired); ?>>
                <?php endif; ?>
              </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          <?php endif; ?>
        </div>
        <div class="modal-footer">
          <?php if($event->payment_required && $event->payment_amount): ?>
            <button type="submit" class="btn btn-success" id="confirmAndPayBtn">Confirm & Pay</button>
          <?php else: ?>
            <button type="submit" class="btn btn-success">Confirm Booking</button>
          <?php endif; ?>
        </div>
      </form>
    </div>
  </div>

  <!-- Add Success Toast/Alert -->
  <div id="bookingSuccessToast" class="toast align-items-center text-bg-success border-0 position-fixed bottom-0 end-0 m-4" role="alert" aria-live="assertive" aria-atomic="true" style="z-index: 9999; display: none; min-width: 320px;">
    <div class="d-flex">
      <div class="toast-body" id="bookingSuccessMessage">
        Booking Confirmed!
            </div>
      <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close" onclick="document.getElementById('bookingSuccessToast').style.display='none';"></button>
                    </div>
                </div>

  <!-- Add Time Slot Modal -->
  <div class="modal fade" id="timeSlotModal" tabindex="-1" aria-labelledby="timeSlotModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white flex-column align-items-start">
          <h5 class="modal-title mb-1" id="timeSlotModalLabel">Available Times</h5>
          <div class="fs-6" id="selectedDateLabel"></div>
          <button type="button" class="btn-close btn-close-white position-absolute end-0 top-0 m-3" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div id="timeSlotsPanel"></div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
  <script>
    // Timezone selector population
    function populateTimezones() {
      const select = document.getElementById('timezoneSelect');
      const timezones = Intl.supportedValuesOf ? Intl.supportedValuesOf('timeZone') : ["Asia/Calcutta", "UTC"];
      const now = new Date();
      timezones.forEach(tz => {
        const tzDate = new Date(now.toLocaleString('en-US', { timeZone: tz }));
        const hours = tzDate.getHours();
        const minutes = tzDate.getMinutes().toString().padStart(2, '0');
        const ampm = hours >= 12 ? 'pm' : 'am';
        const displayHour = ((hours % 12) || 12) + ':' + minutes + ampm;
        const option = document.createElement('option');
        option.value = tz;
        option.textContent = `${tz} (${displayHour})`;
        if (tz === Intl.DateTimeFormat().resolvedOptions().timeZone) option.selected = true;
        select.appendChild(option);
      });
    }
    // Replace FullCalendar with custom calendar rendering
    function renderCustomCalendar(year, month) {
      const calendarEl = document.getElementById('customCalendar');
      calendarEl.innerHTML = '';
      const today = new Date();
      const firstDay = new Date(year, month, 1);
      const lastDay = new Date(year, month + 1, 0);
      const startDay = (firstDay.getDay() + 6) % 7; // Monday as first day
      const daysInMonth = lastDay.getDate();

      // Get weekly availability for styling
      const weeklyAvailability = <?php echo json_encode($event->weekly_availability ?? [], 15, 512) ?>;
      const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      
      // Get event date range settings
      const eventDateRangeType = <?php echo json_encode($event->date_range_type ?? 'indefinitely', 15, 512) ?>;
      const eventDateRangeDays = <?php echo json_encode($event->date_range_days ?? 30, 15, 512) ?>;
      const eventDateRangeStart = <?php echo json_encode($event->date_range_start ?? null, 15, 512) ?>;
      const eventDateRangeEnd = <?php echo json_encode($event->date_range_end ?? null, 15, 512) ?>;
      
      // Header
      const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
      let html = `<div class='d-flex justify-content-between align-items-center mb-2'>
        <button class='btn btn-link p-0' id='prevMonth'>&lt;</button>
        <span class='fw-bold fs-5'>${monthNames[month]} ${year}</span>
        <button class='btn btn-link p-0' id='nextMonth'>&gt;</button>
      </div>`;
      html += `<div class='d-grid calendar-grid-header mb-1' style='grid-template-columns: repeat(7, 1fr);gap:2px;'>`;
      ["MON","TUE","WED","THU","FRI","SAT","SUN"].forEach(d=>{
        html += `<div class='text-center fw-bold' style='height:32px;line-height:32px;'>${d}</div>`;
      });
      html += `</div><div class='d-grid calendar-grid-days' style='grid-template-columns: repeat(7, 1fr);gap:2px;'>`;
      for(let i=0;i<startDay;i++) html += `<div></div>`;
      for(let d=1;d<=daysInMonth;d++) {
        const dateObj = new Date(year, month, d);
        const dateStr = `${year}-${(month+1).toString().padStart(2,'0')}-${d.toString().padStart(2,'0')}`;
        const isToday = dateObj.toDateString() === today.toDateString();
        const isPast = dateObj < new Date(today.getFullYear(),today.getMonth(),today.getDate());

        // Check if this day is within the allowed date range
        let isWithinDateRange = true;
        
        if (eventDateRangeType === 'calendar_days') {
          // Check if date is within the specified number of calendar days
          const maxDate = new Date(today);
          maxDate.setDate(today.getDate() + eventDateRangeDays);
          isWithinDateRange = dateObj <= maxDate;
        } else if (eventDateRangeType === 'date_range') {
          // Check if date is within the specified date range
          if (eventDateRangeStart && eventDateRangeEnd) {
            const startDate = new Date(eventDateRangeStart);
            const endDate = new Date(eventDateRangeEnd);
            isWithinDateRange = dateObj >= startDate && dateObj <= endDate;
          }
        }
        // For 'indefinitely' or any other type, all future dates are allowed

        // Check if this day is available
        const dayOfWeek = dateObj.getDay();
        const dayName = dayNames[dayOfWeek];
        const isDayAvailable = weeklyAvailability[dayName] && weeklyAvailability[dayName].enabled;

        if (!isPast && isWithinDateRange) {
          let buttonStyle = '';
          let buttonClass = 'calendar-day-btn btn btn-sm fw-bold';

          if (isDayAvailable) {
            // Available day - green
            buttonStyle = 'width:40px;height:40px;border-radius:50%;background:#bbf7d0;color:#16a34a;font-weight:700;';
          } else {
            // Unavailable day - gray
            buttonStyle = 'width:40px;height:40px;border-radius:50%;background:#f3f4f6;color:#9ca3af;font-weight:700;cursor:not-allowed;';
            buttonClass += ' disabled';
          }

          if (isToday) {
            buttonStyle += 'border:2px solid #22c55e;';
          }

          html += `<div class='text-center' style='position:relative;'>
            <button class='${buttonClass}' data-date='${dateStr}' style='${buttonStyle}' ${!isDayAvailable ? 'disabled' : ''}>${d}${isToday?'<span style=\"position:absolute;bottom:6px;left:50%;transform:translateX(-50%);width:6px;height:6px;background:#222;border-radius:50%;display:inline-block;\"></span>':''}</button>
          </div>`;
        } else if (!isPast && !isWithinDateRange) {
          // Date is outside the allowed range - show as disabled
          html += `<div class='text-center'><span class='text-muted fw-bold' style='line-height:40px;'>${d}</span></div>`;
        } else {
          // Past date
          html += `<div class='text-center'><span class='text-muted fw-bold' style='line-height:40px;'>${d}</span></div>`;
        }
      }
      html += `</div>`;
      calendarEl.innerHTML = html;
      document.getElementById('prevMonth').onclick = ()=>renderCustomCalendar(month===0?year-1:year,month===0?11:month-1);
      document.getElementById('nextMonth').onclick = ()=>renderCustomCalendar(month===11?year+1:year,month===11?0:month+1);
      document.querySelectorAll('.calendar-day-btn').forEach(btn=>{
        btn.onclick = function() {
          if (!this.disabled) {
            showTimeSlotsPanel(this.dataset.date);
          }
        };
      });
    }
    function showTimeSlotsPanel(dateStr) {
      document.getElementById('selected_date').value = dateStr;
      document.getElementById('selectedDateLabel').textContent = new Date(dateStr).toDateString();

      // Get day of week (0 = Sunday, 1 = Monday, etc.)
      const selectedDate = new Date(dateStr);
      const dayOfWeek = selectedDate.getDay();
      const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      const dayName = dayNames[dayOfWeek];

      // Get event date range settings
      const eventDateRangeType = <?php echo json_encode($event->date_range_type ?? 'indefinitely', 15, 512) ?>;
      const eventDateRangeDays = <?php echo json_encode($event->date_range_days ?? 30, 15, 512) ?>;
      const eventDateRangeStart = <?php echo json_encode($event->date_range_start ?? null, 15, 512) ?>;
      const eventDateRangeEnd = <?php echo json_encode($event->date_range_end ?? null, 15, 512) ?>;

      // Check if selected date is within the allowed range
      const today = new Date();
      let isWithinDateRange = true;
      
      if (eventDateRangeType === 'calendar_days') {
        const maxDate = new Date(today);
        maxDate.setDate(today.getDate() + eventDateRangeDays);
        isWithinDateRange = selectedDate <= maxDate;
      } else if (eventDateRangeType === 'date_range') {
        if (eventDateRangeStart && eventDateRangeEnd) {
          const startDate = new Date(eventDateRangeStart);
          const endDate = new Date(eventDateRangeEnd);
          isWithinDateRange = selectedDate >= startDate && selectedDate <= endDate;
        }
      }

      if (!isWithinDateRange) {
        // Show error message if date is outside allowed range
        const modal = new bootstrap.Modal(document.getElementById('timeSlotModal'));
        document.getElementById('timeSlotsPanel').innerHTML = `
          <div class='alert alert-warning mt-2'>
            <i class="me-2">⚠️</i>This date is outside the allowed booking range for this event.
          </div>
        `;
        modal.show();
        return;
      }

      // Get weekly availability from event data
      const weeklyAvailability = <?php echo json_encode($event->weekly_availability ?? [], 15, 512) ?>;
      console.log('Weekly availability:', weeklyAvailability);
      console.log('Selected day:', dayName);

      let timeSlots = [];

      // Check if the day is available and has slots
      if (weeklyAvailability[dayName] && weeklyAvailability[dayName].enabled && weeklyAvailability[dayName].slots) {
        const daySlots = weeklyAvailability[dayName].slots;
        console.log('Day slots:', daySlots);

        // Generate time slots with 30-minute intervals
        const intervalMinutes = 30; // Fixed 30-minute intervals

        daySlots.forEach(slot => {
          const startTime = slot.start;
          const endTime = slot.end;

          // Convert time strings to minutes
          const startMinutes = timeToMinutes(startTime);
          const endMinutes = timeToMinutes(endTime);

          // Generate 30-minute slots within this time range
          for (let minutes = startMinutes; minutes < endMinutes; minutes += intervalMinutes) {
            const timeStr = minutesToTime(minutes);
            timeSlots.push(timeStr);
          }
        });
      }

      // Fetch booked slots from backend
      fetch(`/calendar-events/<?php echo e($event->id); ?>/booked-slots?date=${dateStr}`)
        .then(response => response.json())
        .then(data => {
          console.log('Booked slots response:', data);

          let bookedSlots = [];
          if (data.success && data.data.booked_slots) {
            bookedSlots = data.data.booked_slots;
          }

          console.log('Booked slots for', dateStr, ':', bookedSlots);

          // Pass all time slots, booked slots, and selected date to render function
          renderTimeSlots(timeSlots, bookedSlots, dayName, weeklyAvailability, dateStr);
        })
        .catch(error => {
          console.error('Error fetching booked slots:', error);
          // Fallback: show all slots if API fails (no booked slots)
          renderTimeSlots(timeSlots, [], dayName, weeklyAvailability, dateStr);
        });
    }

    function renderTimeSlots(allTimeSlots, bookedSlots, dayName, weeklyAvailability, selectedDate) {
        let html = '';
        if (allTimeSlots.length === 0) {
          // Check if the day is not available in weekly availability
          if (!weeklyAvailability[dayName] || !weeklyAvailability[dayName].enabled) {
            html = `<div class='alert alert-info mt-2'>
              <i class="me-2">ℹ️</i>This day is not available for bookings.
            </div>`;
          } else {
            html = `<div class='alert alert-info mt-2'>
              <i class="me-2">ℹ️</i>No time slots available for this date.
            </div>`;
          }
        } else {
          html += `<div class='row g-2'>`;

          // Get current local time and selected date
          const currentLocalTime = new Date();
          const selectedDateObj = new Date(selectedDate);
          const isToday = selectedDateObj.toDateString() === currentLocalTime.toDateString();

          allTimeSlots.forEach((time, idx) => {
            const isBooked = bookedSlots.includes(time);
            let isPast = false;

            // Check if this is a past time slot for today only
            // Buffer time prevents booking slots that are too close to current time
            if (isToday) {
              isPast = isTimeSlotPast(selectedDate, time, 15); // 15 minutes buffer
            }

            // Determine button class and text based on status
            let buttonClass, buttonText, disabled, cursor;

            if (isBooked) {
              buttonClass = 'btn w-100 mb-2 time-slot-btn-disabled';
              buttonText = formatTime(time, document.querySelector('input[name="timeFormat"]:checked').value) + ' (Booked)';
              disabled = 'disabled';
              cursor = 'style="cursor: not-allowed;"';
            } else if (isPast) {
              buttonClass = 'btn w-100 mb-2 time-slot-btn-past';
              buttonText = formatTime(time, document.querySelector('input[name="timeFormat"]:checked').value) + ' (Past)';
              disabled = 'disabled';
              cursor = 'style="cursor: not-allowed;"';
            } else {
              buttonClass = 'btn btn-outline-primary w-100 mb-2 time-slot-btn';
              buttonText = formatTime(time, document.querySelector('input[name="timeFormat"]:checked').value);
              disabled = '';
              cursor = '';
            }

            html += `<div class='col-6'><button type='button' class='${buttonClass}' data-time='${time}' ${disabled} ${cursor}>${buttonText}</button></div>`;
          });
          html += `</div>`;
        }
        document.getElementById('timeSlotsPanel').innerHTML = html;

        // Show modal
        var modal = new bootstrap.Modal(document.getElementById('timeSlotModal'));
        modal.show();

        setTimeout(() => {
          // Only add click handlers to available slots (not disabled ones)
          document.querySelectorAll('.time-slot-btn:not([disabled])').forEach(btn => {
            btn.onclick = function() {
              const selectedDate = document.getElementById('selected_date').value;
              document.getElementById('selected_time').value = this.dataset.time;
              document.getElementById('selectedDateTime').textContent = new Date(selectedDate).toDateString() + ' at ' + formatTime(this.dataset.time, document.querySelector('input[name="timeFormat"]:checked').value);
              document.getElementById('slotConfirmation').classList.remove('d-none');
              document.getElementById('bookBtn').disabled = false;
              document.querySelectorAll('.time-slot-btn:not([disabled])').forEach(b => b.classList.remove('active'));
              this.classList.add('active');
              // Close modal after selection
              bootstrap.Modal.getInstance(document.getElementById('timeSlotModal')).hide();
            };
          });
        }, 50);
    }
    // Helper function to convert time string (HH:MM) to minutes
    function timeToMinutes(timeStr) {
      const [hours, minutes] = timeStr.split(':').map(Number);
      return hours * 60 + minutes;
    }

    // Helper function to convert minutes to time string (HH:MM)
    function minutesToTime(minutes) {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
    }

    // Helper function to check if a time slot is in the past
    function isTimeSlotPast(selectedDate, timeSlot, bufferMinutes = 15) {
      const currentTime = new Date();
      const selectedDateObj = new Date(selectedDate);
      const [hours, minutes] = timeSlot.split(':').map(Number);
      
      // Create the slot datetime
      const slotDateTime = new Date(selectedDateObj);
      slotDateTime.setHours(hours, minutes, 0, 0);
      
      // Add buffer time to current time
      const currentTimeWithBuffer = new Date(currentTime.getTime() + (bufferMinutes * 60 * 1000));
      
      // Debug logging
      console.log(`Time slot check: ${timeSlot} on ${selectedDate}`);
      console.log(`Current time: ${currentTime.toLocaleString()}`);
      console.log(`Slot datetime: ${slotDateTime.toLocaleString()}`);
      console.log(`Current time with buffer: ${currentTimeWithBuffer.toLocaleString()}`);
      console.log(`Is past: ${slotDateTime <= currentTimeWithBuffer}`);
      
      return slotDateTime <= currentTimeWithBuffer;
    }

    // Helper function to convert time between timezones
    function convertTimeToTimezone(time, fromTimezone, toTimezone) {
      if (!fromTimezone || !toTimezone || fromTimezone === toTimezone) {
        return time;
      }
      
      try {
        const [hours, minutes] = time.split(':').map(Number);
        const date = new Date();
        date.setHours(hours, minutes, 0, 0);
        
        const convertedTime = moment(date).tz(fromTimezone).tz(toTimezone);
        return convertedTime.format('HH:mm');
      } catch (error) {
        console.error('Timezone conversion error:', error);
        return time; // Fallback to original time
      }
    }

    function formatTime(time, format) {
        const [hour, minute] = time.split(':');
        let h = parseInt(hour);
      if (format === '24') return `${h.toString().padStart(2, '0')}:${minute}`;
        const ampm = h >= 12 ? 'PM' : 'AM';
        h = h % 12 || 12;
        return `${h}:${minute} ${ampm}`;
    }
    document.addEventListener('DOMContentLoaded', function () {
      populateTimezones();
      const now = new Date();
      renderCustomCalendar(now.getFullYear(), now.getMonth());
      // Fix: define all needed variables
      const selectedDateInput = document.getElementById('selected_date');
      const selectedTimeInput = document.getElementById('selected_time');
      const bookBtn = document.getElementById('bookBtn');
      let timeFormat = document.querySelector('input[name="timeFormat"]:checked')?.value || '12';
      document.querySelectorAll('input[name="timeFormat"]').forEach(radio => {
        radio.addEventListener('change', function() {
          timeFormat = this.value;
          // re-render time slots if open (modal only)
          const date = selectedDateInput.value;
          if(date && document.getElementById('timeSlotModal').classList.contains('show')) {
            showTimeSlotsPanel(date);
          }
        });
      });
    bookBtn.addEventListener('click', function () {
        const date = selectedDateInput.value;
        const time = selectedTimeInput.value;
        if (!date || !time) {
          alert('Please select a date and time slot first.');
          return;
        }

        // Update confirmation modal with selected details
        document.getElementById('confirm-slot-time').textContent = new Date(date).toDateString() + ' at ' + formatTime(time, timeFormat);

        // Update location display in confirmation modal
        const selectedLocationInput = document.querySelector('input[name="selected_location"]:checked');
        const locationDisplay = document.getElementById('confirm-location-display');

        if (selectedLocationInput) {
            const selectedLocation = JSON.parse(selectedLocationInput.value);
            let locationText = selectedLocation.display;

            // Add location type icon
            let locationIcon = '';
            switch(selectedLocation.type) {
                case 'zoom':
                    locationIcon = '🎥 ';
                    break;
                case 'in_person':
                    locationIcon = '📍 ';
                    break;
                case 'phone':
                    locationIcon = '📞 ';
                    break;
                case 'meet':
                    locationIcon = '🎯 ';
                    break;
                case 'skype':
                    locationIcon = '💻 ';
                    break;
                default:
                    locationIcon = '🌐 ';
            }

            locationDisplay.innerHTML = `<span class="badge bg-success">${locationIcon}${locationText}</span>`;
        } else {
            locationDisplay.textContent = 'Location will be provided';
        }

        const modal = new bootstrap.Modal(document.getElementById('bookingFormModal'));
        modal.show();
    });
    document.getElementById('finalBookingForm').addEventListener('submit', function (e) {
        e.preventDefault();
        const name = document.getElementById('attendee_name').value.trim();
        const email = document.getElementById('attendee_email').value.trim();
        const phone = document.getElementById('attendee_phone').value.trim();
        const selectedDate = document.getElementById('selected_date').value;
        const selectedTime = document.getElementById('selected_time').value;
        // Collect custom fields data
        const customFieldsData = {};
        const customFieldInputs = document.querySelectorAll('[name^="custom_fields["]');
        customFieldInputs.forEach(function(input) {
            if (input.value.trim()) {
                const fieldName = input.name.match(/custom_fields\[(.*?)\]/)[1];
                customFieldsData[fieldName] = input.value.trim();
            }
        });
        // Get selected location
        const selectedLocationInput = document.querySelector('input[name="selected_location"]:checked');
        const selectedLocation = selectedLocationInput ? JSON.parse(selectedLocationInput.value) : null;

        if (!name || !email || !selectedDate || !selectedTime) {
            alert("Please fill in all required fields and select a date/time.");
            return;
        }

        // Get selected timezone
        const selectedTimezone = document.getElementById('timezoneSelect').value || 'Asia/Calcutta';

        // Prepare data for backend
        const bookingData = {
            event_id: <?php echo e($event->id); ?>,
            name: name,
            email: email,
            phone: phone,
            date: selectedDate,
            time: selectedTime,
            selected_location: selectedLocation,
            timezone: selectedTimezone,
            custom_fields: Object.keys(customFieldsData),
            custom_fields_value: Object.values(customFieldsData),
            _token: '<?php echo e(csrf_token()); ?>'
        };
        // Show loading state
        const submitBtn = document.querySelector('#finalBookingForm button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Creating Booking...';
        // Check if payment is required
        const isPaymentRequired = <?php echo e($event->payment_required ? 'true' : 'false'); ?>;
        const paymentAmount = <?php echo e($event->payment_amount ?? 0); ?>;
        const partialPaymentEnabled = <?php echo e($event->partial_payment_enabled ? 'true' : 'false'); ?>;
        const partialPaymentPercentage = <?php echo e($event->partial_payment_percentage ?? 0); ?>;
        
        if (isPaymentRequired && paymentAmount > 0) {
            // Check payment type selection
            const paymentType = document.querySelector('input[name="payment_type"]:checked')?.value || 'full';
            
            if (paymentType === 'partial' && partialPaymentEnabled && partialPaymentPercentage > 0) {
                // Calculate partial payment amount
                const partialAmount = (paymentAmount * partialPaymentPercentage) / 100;
                bookingData.payment_amount = partialAmount;
                bookingData.payment_type = 'partial';
                bookingData.partial_payment_percentage = partialPaymentPercentage;
                bookingData.remaining_amount = paymentAmount - partialAmount;
            } else {
                // Full payment
                bookingData.payment_amount = paymentAmount;
                bookingData.payment_type = 'full';
            }
        }

        // Send data to backend
        fetch('<?php echo e(route("public-bookings.store")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                'Accept': 'application/json'
            },
            body: JSON.stringify(bookingData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // If payment is required, initialize payment
                if (isPaymentRequired && paymentAmount > 0) {
                    // Check if Razorpay is configured
                    const razorpayKey = "<?php echo e(isset($company_payment_setting['razorpay_public_key']) ? $company_payment_setting['razorpay_public_key'] : ''); ?>";
                    if (!razorpayKey) {
                        alert('Razorpay payment is not configured for this event. Please contact the event organizer.');
                        return;
                    }
                    
                    // Get the actual payment amount based on payment type
                    const actualPaymentAmount = bookingData.payment_amount || paymentAmount;
                    
                    // Initialize payment
                    fetch('<?php echo e(route("public.booking-payments.initialize")); ?>', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify({
                            booking_id: data.data.id,
                            amount: actualPaymentAmount
                        })
                    })
                    .then(response => response.json())
                    .then(paymentData => {
                        console.log('Payment initialization response:', paymentData);
                        if (paymentData.flag === 1) {
                            // Initialize Razorpay payment
                            const options = {
                                "key": paymentData.razorpay_key || "<?php echo e(isset($company_payment_setting['razorpay_public_key']) ? $company_payment_setting['razorpay_public_key'] : ''); ?>",
                                "amount": paymentData.total_price * 100,
                                "currency": paymentData.currency,
                                "name": "<?php echo e($event->title); ?>",
                                "description": "Booking payment for <?php echo e($event->title); ?>",
                                "handler": function(response) {
                                    // First verify the payment via callback
                                    fetch('<?php echo e(url("/public/booking-payments/callback")); ?>/' + response.razorpay_payment_id + '/' + data.data.id + '?amount=' + actualPaymentAmount, {
                                        method: 'GET',
                                        headers: {
                                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                                            'X-Requested-With': 'XMLHttpRequest'
                                        }
                                    })
                                    .then(response => {
                                        // Redirect to confirmation page regardless of callback response
                                        window.location.href = '<?php echo e(url("booking-confirmation")); ?>/' + data.data.id + '?success=1';
                                    })
                                    .catch(error => {
                                        console.error('Payment verification error:', error);
                                        // Still redirect to confirmation page
                                        window.location.href = '<?php echo e(url("booking-confirmation")); ?>/' + data.data.id + '?success=1';
                                    });
                                },
                                "prefill": {
                                    "email": email,
                                    "name": name
                                },
                                "theme": {
                                    "color": "#3C8764"
                                }
                            };
                            console.log('Razorpay options:', options);
                            console.log('Amount details - total_price:', paymentData.total_price, 'amount in paise:', paymentData.total_price * 100, 'currency:', paymentData.currency);
                            console.log('Razorpay key from payment data:', paymentData.razorpay_key);
                            console.log('Razorpay key from settings:', "<?php echo e(isset($company_payment_setting['razorpay_public_key']) ? $company_payment_setting['razorpay_public_key'] : ''); ?>");
                            
                            // Check if Razorpay key is available
                            if (!options.key) {
                                throw new Error('Razorpay public key is not configured');
                            }
                            
                            try {
                                if (typeof Razorpay === 'undefined') {
                                    throw new Error('Razorpay script not loaded');
                                }
                                const rzp = new Razorpay(options);
                                rzp.open();
                            } catch (error) {
                                console.error('Razorpay initialization error:', error);
                                alert('Payment gateway initialization failed. Please try again.');
                            }
                        } else {
                            console.error('Payment initialization failed:', paymentData);
                            alert('Payment initialization failed: ' + (paymentData.message || 'Unknown error'));
                        }
                    })
                    .catch(error => {
                        console.error('Payment error:', error);
                        console.error('Payment error details:', error.message);
                        alert('Payment initialization failed. Please try again.');
                    });
                } else {
                    // No payment required, redirect to confirmation page
                    if (data.redirect_url) {
                        window.location.href = data.redirect_url;
                    } else {
                        // Fallback to confirmation page
                        window.location.href = '<?php echo e(url("booking-confirmation")); ?>/' + data.data.id;
                    }
                    let slotRow = document.getElementById('inlineTimeSlots');
                    if (slotRow) slotRow.remove();
                }
            } else {
                alert('Error: ' + (data.message || 'Failed to create booking'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error creating booking. Please try again.');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });
});
</script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\new_laravel_project\omx-new-saas\resources\views/tasks/copy_event.blade.php ENDPATH**/ ?>