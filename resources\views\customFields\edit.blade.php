{{ Form::model($customField, array('route' => array('custom-field.update', $customField->id), 'method' => 'PUT', 'class'=>'needs-validation', 'novalidate', 'id' => 'updateCustomFieldForm')) }}
<div class="modal-body">
    <div class="row">
        <div class="form-group col-md-12">
            {{Form::label('name',__('Custom Field Name'),['class'=>'form-label'])}}<x-required></x-required>
            {{Form::text('name',null,array('class'=>'form-control','required'=>'required', 'placeholder'=>__('Enter Custom Field Name')))}}
        </div>

        <div class="form-group col-md-12">
            {{Form::label('type',__('Type'),['class'=>'form-label'])}}
            {{Form::select('type', $types, null, array('class'=>'form-control select', 'disabled' => 'disabled'))}}
        </div>

        <div class="form-group col-md-12">
            {{Form::label('module',__('Module'),['class'=>'form-label'])}}
            {{Form::select('module', $modules, null, array('class'=>'form-control select', 'disabled' => 'disabled'))}}
        </div>

        <div class="form-group col-md-12">
            {{Form::label('is_required',__('Is Required'),['class'=>'form-label'])}}
            {{Form::select('is_required', [0 => __('No'), 1 => __('Yes')], null, array('class'=>'form-control select'))}}
        </div>

        <div class="form-group col-md-12">
            {{Form::label('status',__('Status'),['class'=>'form-label'])}}
            {{Form::select('status', [1 => __('Active'), 0 => __('Inactive')], null, array('class'=>'form-control select'))}}
        </div>

        @if(in_array($customField->type, ['checkbox', 'radio', 'select', 'multiselect']))
        <div class="form-group col-md-12">
            {{Form::label('options',__('Options'),['class'=>'form-label'])}}
            <div id="options-list">
                @if($customField->options && is_array($customField->options))
                    @foreach($customField->options as $option)
                        <div class="d-flex mb-2">
                            {{Form::text('options[]', $option, array('class'=>'form-control me-2', 'placeholder'=>__('Option')))}}
                            <button type="button" class="btn btn-sm btn-danger remove-option">-</button>
                        </div>
                    @endforeach
                @else
                    <div class="d-flex mb-2">
                        {{Form::text('options[]', null, array('class'=>'form-control me-2', 'placeholder'=>__('Option')))}}
                        <button type="button" class="btn btn-sm btn-success add-option">+</button>
                    </div>
                @endif
            </div>
        </div>
        @endif
    </div>
</div>

    <div class="modal-footer">
        <input type="button" value="{{__('Cancel')}}" class="btn  btn-secondary" data-bs-dismiss="modal">
        <input type="submit" value="{{__('Update')}}" class="btn  btn-primary" id="update-custom-field-btn">
    </div>
{{ Form::close() }}

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const optionsList = document.getElementById('options-list');
        
        if (optionsList) {
            function addOptionInput() {
                const inputDiv = document.createElement('div');
                inputDiv.className = 'd-flex mb-2';
                inputDiv.innerHTML = `
                    <input type="text" name="options[]" class="form-control me-2" placeholder="{{ __('Option') }}">
                    <button type="button" class="btn btn-sm btn-danger remove-option">-</button>
                `;
                optionsList.appendChild(inputDiv);
            }

            optionsList.addEventListener('click', function (e) {
                if (e.target.classList.contains('remove-option')) {
                    e.target.closest('.d-flex').remove();
                }
            });

            // Add option button
            document.addEventListener('click', function (e) {
                if (e.target.classList.contains('add-option')) {
                    addOptionInput();
                }
            });
        }
    });

    // Handle form submission via AJAX
    $('#updateCustomFieldForm').on('submit', function(e) {
        e.preventDefault();
        
        var form = $(this);
        var submitBtn = $('#update-custom-field-btn');
        var originalText = submitBtn.val();
        
        submitBtn.val('Updating...').prop('disabled', true);
        
        // Collect options data
        var options = [];
        $('input[name="options[]"]').each(function() {
            var value = $(this).val().trim();
            if (value) {
                options.push(value);
            }
        });
        
        // Create form data
        var formData = form.serialize();
        
        $.ajax({
            url: form.attr('action'),
            type: 'POST',
            data: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.success) {
                    // Show success toast
                    showToast('success', response.message);
                    
                    // Close modal
                    $('.modal').modal('hide');
                    
                    // Refresh the custom fields table
                    if (response.custom_fields) {
                        refreshCustomFieldsTable(response.custom_fields);
                    } else {
                        // Fallback to page reload if no data provided
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    }
                } else {
                    showToast('error', response.message || 'Error updating custom field');
                }
            },
            error: function(xhr) {
                if (xhr.responseJSON && xhr.responseJSON.errors) {
                    var errorMessage = '';
                    $.each(xhr.responseJSON.errors, function(key, value) {
                        errorMessage += value[0] + '\n';
                    });
                    showToast('error', 'Validation errors:\n' + errorMessage);
                } else {
                    showToast('error', 'Error updating custom field');
                }
            },
            complete: function() {
                submitBtn.val(originalText).prop('disabled', false);
            }
        });
    });

    // Toast notification function
    function showToast(type, message) {
        // Check if toast container exists, if not create it
        if (!$('#toast-container').length) {
            $('body').append('<div id="toast-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>');
        }
        
        var toastClass = type === 'success' ? 'bg-success' : 'bg-danger';
        var icon = type === 'success' ? 'ti ti-check' : 'ti ti-alert-circle';
        
        var toast = $(`
            <div class="toast align-items-center text-white border-0 ${toastClass}" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="${icon} me-2"></i>${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `);
        
        $('#toast-container').append(toast);
        
        // Initialize and show toast
        var bsToast = new bootstrap.Toast(toast[0]);
        bsToast.show();
        
        // Remove toast after it's hidden
        toast.on('hidden.bs.toast', function() {
            $(this).remove();
        });
    }

    // Function to refresh custom fields table
    function refreshCustomFieldsTable(customFields) {
        var tbody = $('table tbody');
        tbody.empty();
        
        if (customFields.length === 0) {
            tbody.append(`
                <tr>
                    <td colspan="7" class="text-center py-5">
                        <div class="text-muted">
                            <i class="ti ti-inbox fs-1 mb-3 d-block"></i>
                            <h5>{{ __('No custom fields found') }}</h5>
                            <p>{{ __('Create your first custom field to get started') }}</p>
                        </div>
                    </td>
                </tr>
            `);
            return;
        }
        
        customFields.forEach(function(field) {
            var requiredBadge = field.is_required ? 
                '<div class="chip-chip bg-success-subtle text-success"><i class="ti ti-check me-1"></i><span class="chip-text">{{ __("Yes") }}</span></div>' :
                '<div class="chip-chip bg-secondary-subtle text-secondary"><i class="ti ti-minus me-1"></i><span class="chip-text">{{ __("No") }}</span></div>';
            
            var statusToggle = field.status ? 'checked' : '';
            var statusLabel = field.status ? '{{ __("Active") }}' : '{{ __("Inactive") }}';
            
            var row = `
                <tr class="border-bottom">
                    <td class="py-3 px-4 text-center align-middle">
                        <div class="form-check">
                            <input class="form-check-input row-checkbox" type="checkbox" 
                                   value="${field.id}" 
                                   id="field-${field.id}">
                        </div>
                    </td>
                    <td class="py-3 px-4 align-middle">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm bg-primary-subtle rounded-circle d-flex align-items-center justify-content-center me-3">
                                <i class="ti ti-forms text-primary"></i>
                            </div>
                            <div>
                                <h6 class="mb-0 fw-medium">${field.name}</h6>
                            </div>
                        </div>
                    </td>
                    <td class="py-3 px-4 text-center align-middle">
                        <div class="chip-chip bg-primary-subtle text-primary">
                            <i class="ti ti-type me-1"></i>
                            <span class="chip-text">${field.type}</span>
                        </div>
                    </td>
                    <td class="py-3 px-4 text-center align-middle">
                        <div class="chip-chip bg-info-subtle text-info">
                            <i class="ti ti-layers me-1"></i>
                            <span class="chip-text">${field.module}</span>
                        </div>
                    </td>
                    <td class="py-3 px-4 text-center align-middle">
                        ${requiredBadge}
                    </td>
                    <td class="py-3 px-4 text-center align-middle">
                        <div class="form-check form-switch d-flex justify-content-center">
                            <input class="form-check-input status-toggle" type="checkbox" 
                                   id="status-${field.id}" 
                                   data-field-id="${field.id}"
                                   ${statusToggle}
                                   style="width: 3rem; height: 1.5rem;">
                            <label class="form-check-label ms-2 fw-medium" for="status-${field.id}">
                                ${statusLabel}
                            </label>
                        </div>
                    </td>
                    <td class="py-3 px-4 text-center align-middle">
                        <div class="d-flex justify-content-center gap-2">
                            <a href="#" class="btn btn-outline-primary btn-sm px-3" 
                               data-url="{{ route('custom-field.edit', '') }}/${field.id}" 
                               data-ajax-popup="true" 
                               data-title="{{ __('Edit Custom Field') }}"
                               title="{{ __('Edit') }}">
                                <i class="ti ti-pencil me-1"></i>{{ __('Edit') }}
                            </a>
                            <form method="DELETE" action="{{ route('custom-field.destroy', '') }}/${field.id}" style="display:inline">
                                <button type="submit" class="btn btn-outline-danger btn-sm px-3 delete-field-btn" 
                                        data-field-id="${field.id}"
                                        title="{{ __('Delete') }}">
                                    <i class="ti ti-trash me-1"></i>{{ __('Delete') }}
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
        
        // Reinitialize any necessary event handlers
        initializeTableHandlers();
    }

    // Function to initialize table event handlers
    function initializeTableHandlers() {
        // Reinitialize status toggle handlers
        $('.status-toggle').off('change').on('change', function() {
            var fieldId = $(this).data('field-id');
            var status = $(this).is(':checked');
            toggleFieldStatus(fieldId, status);
        });
        
        // Reinitialize checkbox handlers
        $('.row-checkbox').off('change').on('change', function() {
            updateSelectAllState();
            updateBulkActionsVisibility();
        });
        
        // Reinitialize delete handlers
        $('.delete-field-btn').off('click').on('click', function(e) {
            e.preventDefault();
            var fieldId = $(this).data('field-id');
            if (confirm('{{ __("Are you sure you want to delete this custom field?") }}')) {
                deleteField(fieldId);
            }
        });
    }

    // Helper functions for table operations
    function toggleFieldStatus(fieldId, status) {
        fetch('{{ route("custom-field.toggle-status") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                field_id: fieldId,
                status: status
            })
        }).then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('success', data.message);
            } else {
                showToast('error', data.message || 'Error updating status');
            }
        }).catch(error => {
            showToast('error', 'Error updating status');
        });
    }

    function updateSelectAllState() {
        var totalCheckboxes = $('.row-checkbox').length;
        var checkedCheckboxes = $('.row-checkbox:checked').length;
        var selectAllCheckbox = $('#select-all');
        
        if (checkedCheckboxes === 0) {
            selectAllCheckbox.prop('indeterminate', false).prop('checked', false);
        } else if (checkedCheckboxes === totalCheckboxes) {
            selectAllCheckbox.prop('indeterminate', false).prop('checked', true);
        } else {
            selectAllCheckbox.prop('indeterminate', true);
        }
    }

    function updateBulkActionsVisibility() {
        var checkedCount = $('.row-checkbox:checked').length;
        var bulkActionsBar = $('#bulk-actions-bar');
        
        if (checkedCount > 0) {
            bulkActionsBar.show();
            $('#selected-count').text(checkedCount);
        } else {
            bulkActionsBar.hide();
        }
    }

    function deleteField(fieldId) {
        fetch(`/custom-field/${fieldId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        }).then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('success', data.message);
                // Refresh the table
                refreshCustomFieldsTable(data.custom_fields || []);
            } else {
                showToast('error', data.message || 'Error deleting field');
            }
        }).catch(error => {
            showToast('error', 'Error deleting field');
        });
    }
</script>
