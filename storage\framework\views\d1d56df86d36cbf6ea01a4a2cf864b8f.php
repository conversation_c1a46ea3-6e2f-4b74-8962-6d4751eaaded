
<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Manage Leads')); ?> <?php if($pipeline): ?>
        - <?php echo e($pipeline->name); ?>

    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css-page'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/summernote/summernote-bs4.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/plugins/dragula.min.css')); ?>" id="main-style-link">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <style>
        :root {
            --kanban-bg: rgba(255,255,255,0.7);
            --kanban-blur: 16px;
            --kanban-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.10);
            --kanban-border: 1px solid rgba(255,255,255,0.18);
            --kanban-radius: 18px;
            --kanban-gradient: linear-gradient(135deg, rgba(245,247,250,0.8) 0%, rgba(230,240,255,0.7) 100%);
            --kanban-label-radius: 12px;
            --kanban-label-font: 13px;
            --kanban-label-padding: 3px 12px;
        }
        .kanban-wrapper {
            display: flex;
            gap: 24px;
            overflow-x: auto;
            padding-bottom: 16px;
            scrollbar-width: thin;
        }
        .kanban-wrapper::-webkit-scrollbar {
            height: 8px;
        }
        .kanban-wrapper::-webkit-scrollbar-thumb {
            background: #e0e7ef;
            border-radius: 8px;
        }
        .kanban-col {
            min-width: 320px;
            max-width: 350px;
            flex: 1 0 320px;
            background: var(--kanban-gradient);
            border-radius: var(--kanban-radius);
            box-shadow: var(--kanban-shadow);
            border: var(--kanban-border);
            backdrop-filter: blur(var(--kanban-blur));
            padding: 0 0 12px 0;
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .kanban-header {
            padding: 20px 20px 10px 20px;
            border-bottom: 1px solid #f0f0f0;
            background: transparent;
            border-radius: var(--kanban-radius) var(--kanban-radius) 0 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .kanban-header h4 {
            font-size: 1.1rem;
            font-weight: 700;
            margin: 0;
        }
        .kanban-header .count {
            background: #e3e9f7;
            color: #3a3a3a;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1rem;
        }
        .sales-item-wrp {
            padding: 16px 12px 0 12px;
            min-height: 80px;
            max-height: 480px; /* Show ~3 cards, then scroll */
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #e0e7ef #f8fafc;
            transition: max-height 0.2s;
        }
        .sales-item-wrp::-webkit-scrollbar {
            width: 8px;
        }
        .sales-item-wrp::-webkit-scrollbar-thumb {
            background: #e0e7ef;
            border-radius: 8px;
        }
        .sales-item-wrp::-webkit-scrollbar-track {
            background: #f8fafc;
            border-radius: 8px;
        }
        .kanban-card {
            background: var(--kanban-bg);
            border-radius: var(--kanban-radius);
            box-shadow: 0 2px 12px 0 rgba(31, 38, 135, 0.08);
            border: var(--kanban-border);
            margin-bottom: 18px;
            padding: 18px 16px 12px 16px;
            position: relative;
            transition: box-shadow 0.2s, transform 0.2s;
            cursor: grab;
            backdrop-filter: blur(var(--kanban-blur));
        }
        .kanban-card.dragging {
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.18);
            transform: scale(1.03);
            z-index: 10;
        }
        .kanban-card .card-top {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
        }
        .kanban-card .lead-title {
            font-size: 1.05rem;
            font-weight: 600;
            color: #222;
            margin-bottom: 2px;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        .kanban-card .lead-title i {
            color: #6c63ff;
            font-size: 1rem;
        }
        .kanban-card .badge-wrp {
            margin-top: 6px;
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }
        .kanban-label {
            border-radius: var(--kanban-label-radius);
            font-size: var(--kanban-label-font);
            padding: var(--kanban-label-padding);
            font-weight: 500;
            color: #fff;
            background: #6c63ff;
            opacity: 0.95;
        }
        .kanban-label.bg-light-success { background: #28a745; }
        .kanban-label.bg-light-danger { background: #dc3545; }
        .kanban-label.bg-light-warning { background: #ffc107; color: #222; }
        .kanban-label.bg-light-info { background: #17a2b8; }
        .kanban-label.bg-light-primary { background: #6c63ff; }
        .kanban-label.bg-light-secondary { background: #6c757d; }
        .kanban-label.bg-light-brown { background: #a17a69; }
        .kanban-label.bg-light-blue { background: #007bff; }
        .kanban-label.bg-light-purple { background: #6f42c1; }
        .kanban-card .contact-info {
            margin-top: 10px;
            display: flex;
            flex-direction: column;
            gap: 6px;
        }
        .kanban-card .contact-item {
            display: flex;
            align-items: center;
            gap: 7px;
            font-size: 14px;
            color: #555;
        }
        .kanban-card .contact-item i {
            color: #888;
        }
        .kanban-card .card-bottom {
            margin-top: 14px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .kanban-card .communication-buttons {
            display: flex;
            gap: 8px;
        }
        .kanban-card .communication-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.12);
            background: #6c63ff;
        }
        .kanban-card .communication-btn.call { background: #28a745; }
        .kanban-card .communication-btn.whatsapp { background: #25D366; }
        .kanban-card .communication-btn.email { background: #6f42c1; }
        .kanban-card .communication-btn.activity { background: #fd7e14; }
        .kanban-card .communication-btn i {
            font-size: 14px;
        }
        .kanban-card .user-group {
            display: flex;
            gap: 4px;
        }
        .kanban-card .user-group i {
            font-size: 22px;
            color: #6c63ff;
        }
        .kanban-card .drag-handle {
            cursor: grab;
            color: #bdbdbd;
            font-size: 18px;
            margin-right: 8px;
            transition: color 0.2s;
        }
        .kanban-card .drag-handle:hover {
            color: #6c63ff;
        }
        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
            margin: 3px !important;
        }
        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
            color: black !important;
        }
        /* Responsive */
        @media (max-width: 900px) {
            .kanban-wrapper {
                gap: 12px;
            }
            .kanban-col {
                min-width: 260px;
                max-width: 100vw;
            }
            .sales-item-wrp {
                max-height: 340px;
            }
        }
        @media (max-width: 600px) {
            .kanban-wrapper {
                gap: 8px;
            }
            .kanban-col {
                min-width: 90vw;
                max-width: 98vw;
                padding: 0;
            }
            .kanban-header {
                padding: 14px 10px 8px 10px;
            }
            .sales-item-wrp {
                max-height: 220px;
                padding: 8px 4px 0 4px;
            }
            .kanban-card {
                padding: 12px 8px 8px 8px;
            }
        }

        /* Action Buttons Responsive Styles */
        @media (max-width: 768px) {
            
            .search-container {
                order: 1;
                width: 100% !important;
                margin-right: 0 !important;
                margin-bottom: 0.5rem;
            }
            
            .search-input {
                width: 100% !important;
                min-width: auto !important;
            }
            
            #change-pipeline {
                order: 2;
                width: 100% !important;
                margin-bottom: 0.5rem;
            }
            
            #default_pipeline_id {
                width: 100% !important;
                min-width: auto !important;
            }
            
            .action-buttons-row {
                order: 3;
                display: flex !important;
                flex-wrap: wrap !important;
                gap: 0.5rem !important;
                justify-content: center !important;
                width: 100% !important;
            }
            
            .action-btn {
                width: 45px !important;
                height: 45px !important;
                margin-right: 0 !important;
                flex-shrink: 0;
            }
        }
        
        @media (max-width: 480px) {
            .action-buttons-row {
                gap: 0.25rem !important;
            }
            
            .action-btn {
                width: 40px !important;
                height: 40px !important;
            }
            
            .action-btn i {
                font-size: 14px !important;
            }
        }
    </style>
    <style>
        .modern-comm-modal {
            background: rgba(255,255,255,0.85);
            border-radius: 20px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.18);
            border: 1px solid rgba(255,255,255,0.18);
            backdrop-filter: blur(12px);
        }
        .comm-lead-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6c63ff 0%, #17a2b8 100%);
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            box-shadow: 0 2px 8px rgba(31,38,135,0.10);
        }
        .comm-lead-name {
            font-weight: 600;
            font-size: 1.1rem;
        }
        .comm-lead-contact {
            font-size: 0.95rem;
        }
        .communication-options-list {
            display: flex;
            flex-direction: column;
            gap: 14px;
            margin-top: 10px;
        }
        .communication-option-item {
            display: flex;
            align-items: center;
            padding: 14px 18px;
            border-radius: 12px;
            background: rgba(245,247,250,0.95);
            color: #333;
            text-decoration: none;
            font-size: 1.08rem;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(31,38,135,0.06);
            transition: all 0.2s;
            border: 1px solid #f0f0f0;
        }
        .communication-option-item:hover {
            background: linear-gradient(135deg, #e3e9f7 0%, #f8fafc 100%);
            transform: translateX(5px) scale(1.03);
            box-shadow: 0 4px 16px rgba(31,38,135,0.10);
        }
        .communication-option-item i {
            font-size: 1.5rem;
            margin-right: 18px;
            width: 32px;
            text-align: center;
        }
        #whatsapp-option i { color: #25D366; }
        #default-email-option i { color: #007BFF; }
        #cloud-email-option i { color: #6f42c1; }
        #sms-option i { color: #17a2b8; }
        @media (max-width: 600px) {
            .modern-comm-modal {
                border-radius: 10px;
            }
            .communication-option-item {
                padding: 12px 10px;
                font-size: 1rem;
            }
            .comm-lead-avatar {
                width: 38px;
                height: 38px;
                font-size: 1.1rem;
            }
        }
        
        /* Oval Pipeline Select Styling */
        #default_pipeline_id {
            border-radius: 25px !important;
            padding: 8px 20px 8px 20px !important;
            border: 2px solid #e0e7ef !important;
            background: #ffffff !important;
            color: #374151 !important;
            font-weight: 500 !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            min-width: 180px !important;
            height: 40px !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%236c63ff'%3e%3cpath d='M8 11L3 6h10l-5 5z'/%3e%3c/svg%3e") !important;
            background-position: right 12px center !important;
            background-repeat: no-repeat !important;
            background-size: 16px 12px !important;
            padding-right: 35px !important;
            appearance: none !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
        }
        
        #default_pipeline_id:hover {
            border-color: #6c63ff !important;
            box-shadow: 0 4px 12px rgba(108, 99, 255, 0.2) !important;
            transform: translateY(-1px) !important;
        }
        
        #default_pipeline_id:focus {
            outline: none !important;
            border-color: #6c63ff !important;
            box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.1) !important;
            background: #ffffff !important;
        }
        
        #default_pipeline_id option {
            background: white !important;
            color: #374151 !important;
            padding: 8px 12px !important;
            border-radius: 8px !important;
        }
        
        /* Custom form wrapper styling for oval design */
        #change-pipeline {
            background: transparent !important;
            border: none !important;
            padding: 0 !important;
            display: inline-flex !important;
            align-items: center !important;
            height: 40px !important;
            margin-bottom: 0px;
        }
        

        
        /* Action buttons row styling for desktop */
        .action-buttons-row {
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
        }
        
        /* Search Bar Styling - Same oval shape as pipeline select */
        .search-container {
            position: relative !important;
            display: inline-flex !important;
            align-items: center !important;
            height: 40px !important;
        }
        
        .search-input {
            border-radius: 25px !important;
            padding: 6px 14px 6px 14px !important;
            padding-right: 45px !important;
            border: 2px solid #e0e7ef !important;
            background: #ffffff !important;
            color: #374151 !important;
            font-weight: 500 !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            min-width: 200px !important;
            height: 40px !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            appearance: none !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
            transition: all 0.3s ease !important;
        }
        
        .search-input:focus {
            outline: none !important;
            border-color: #6c63ff !important;
            box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.1) !important;
            background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%) !important;
        }
        
        .search-input:hover {
            border-color: #6c63ff !important;
            box-shadow: 0 4px 12px rgba(108, 99, 255, 0.2) !important;
            transform: translateY(-1px) !important;
        }
        
        .search-input::placeholder {
            color: #9ca3af !important;
            font-weight: 400 !important;
        }
        
        .search-icon {
            position: absolute !important;
            right: 15px !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
            color: #6c63ff !important;
            font-size: 14px !important;
            cursor: pointer !important;
            z-index: 10 !important;
        }
        
        .clear-icon {
            position: absolute !important;
            right: 15px !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
            color: #6c63ff !important;
            font-size: 14px !important;
            cursor: pointer !important;
            z-index: 10 !important;
            transition: color 0.3s ease !important;
        }
        
        .clear-icon:hover {
            color: #dc3545 !important;
        }



        /* Loading state for kanban */
        .kanban-wrapper.loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .kanban-wrapper.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #10b981;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            z-index: 1000;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* Filter Panel Styles */
        .filter-panel {
            position: fixed;
            top: 0;
            right: -400px;
            width: 380px;
            height: 100%;
            background: #fff;
            box-shadow: -2px 0 8px rgba(0,0,0,0.1);
            transition: right 0.3s ease;
            z-index: 1050;
            padding: 0;
            display: flex;
            flex-direction: column;
        }
        .filter-panel.open {
            right: 0;
        }
        .filter-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1049;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        .filter-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        .filter-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }
        .filter-header .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .filter-header .back-btn {
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        .filter-header .back-btn:hover {
            background: #e5e7eb;
            color: #374151;
        }
        .filter-header .filter-icon {
            color: #3b82f6;
            font-size: 16px;
        }
        .filter-header h4 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }
        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            color: #6b7280;
            cursor: pointer;
            padding: 4px;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        .close-btn:hover {
            background: #e5e7eb;
            color: #374151;
        }
        .filter-content {
            flex: 1;
            overflow: hidden;
            padding: 24px 20px 0 20px;
            display: flex;
            flex-direction: column;
        }
        .filter-section {
            margin-bottom: 24px;
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }
        .filter-section-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
        }
        .user-search {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 16px;
            background: #f9fafb;
            color: #6b7280;
            flex-shrink: 0;
        }
        .user-search:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: #fff;
            color: #1f2937;
        }
        .user-search::placeholder {
            color: #9ca3af;
        }
        .user-list {
            flex: 1;
            overflow-y: auto;
            min-height: 0;
        }
        .user-item {
            display: flex;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .user-item:last-child {
            border-bottom: none;
        }
        .user-item:hover {
            background: #f9fafb;
        }
        .user-item input[type="checkbox"] {
            margin-right: 16px;
            width: 18px;
            height: 18px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            cursor: pointer;
        }
        .user-item input[type="checkbox"]:checked {
            background-color: #3b82f6;
            border-color: #3b82f6;
        }
        .user-item label {
            margin: 0;
            cursor: pointer;
            flex: 1;
            font-size: 15px;
            color: #374151;
            font-weight: 400;
        }
        .filter-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            border-top: 1px solid #e5e7eb;
            background: #f9fafb;
            margin-top: auto;
        }
        .filter-footer .btn {
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .filter-footer .btn-link {
            background: none;
            color: #ef4444;
            text-decoration: none;
            padding: 10px 16px;
        }
        .filter-footer .btn-link:hover {
            background: #fef2f2;
            color: #dc2626;
        }
        .filter-footer .btn-secondary {
            background: #6b7280;
            color: white;
        }
        .filter-footer .btn-secondary:hover {
            background: #4b5563;
        }
        .filter-footer .btn-success {
            background: #10b981;
            color: white;
        }
        .filter-footer .btn-success:hover {
            background: #059669;
        }
        .filter-applied-indicator {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 12px;
            height: 12px;
            background: #fbbf24;
            border-radius: 50%;
            border: 2px solid white;
            display: none;
            animation: pulse 2s infinite;
        }
        #openFilterPanel.has-filters .filter-applied-indicator {
            display: block;
        }
        #openFilterPanel.has-filters {
            background: linear-gradient(to right, #dc2626, #ef4444) !important;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Filter Categories Styles */
        .filter-categories {
            padding: 0;
        }
        .filter-category-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 0;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .filter-category-item:last-child {
            border-bottom: none;
        }
        .filter-category-item:hover {
            background: #f9fafb;
        }
        .category-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .category-icon {
            color: #6b7280;
            font-size: 16px;
            width: 20px;
        }
        .category-name {
            font-size: 15px;
            color: #374151;
            font-weight: 400;
        }
        .category-arrow {
            color: #9ca3af;
            font-size: 12px;
        }
        .filter-view {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        /* Date Range Picker Styles */
        .date-range-picker {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            background: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        .date-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        .date-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }
        .date-separator {
            color: #6b7280;
            font-weight: 500;
        }
        .date-option input[type="radio"] {
            margin-right: 16px;
            width: 18px;
            height: 18px;
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('script-page'); ?>
    <script src="<?php echo e(asset('css/summernote/summernote-bs4.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/plugins/dragula.min.js')); ?>"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        ! function(a) {
            "use strict";
            var t = function() {
                this.$body = a("body");
                this.dragulaInstance = null;
                this.dragTimeout = null;
            };

            t.prototype.init = function() {
                var self = this;

                a('[data-plugin="dragula"]').each(function() {
                    var $wrapper = a(this);
                    var containers = $wrapper.data("containers");
                    var containerElements = [];

                    if (containers) {
                        for (var i = 0; i < containers.length; i++) {
                            var element = a("#" + containers[i])[0];
                            if (element) containerElements.push(element);
                        }
                    } else {
                        containerElements = [$wrapper[0]];
                    }

                    var handleClass = $wrapper.data("handleclass");

                    // Destroy existing instance if it exists
                    if (self.dragulaInstance) {
                        self.dragulaInstance.destroy();
                    }

                    // Create optimized dragula instance
                    self.dragulaInstance = dragula(containerElements, {
                        moves: function(el, container, handle) {
                            if (handleClass) {
                                return handle.classList.contains(handleClass);
                            }
                            // Allow dragging on card but not on interactive elements
                            return !a(handle).closest('a, button, .btn, .dropdown-menu, input, textarea, select').length;
                        },
                        accepts: function(el, target, source, sibling) {
                            return a(target).hasClass('sales-item-wrp');
                        },
                        removeOnSpill: false,
                        revertOnSpill: true,
                        direction: 'vertical',
                        mirrorContainer: document.body
                    });

                    // Add optimized event handlers
                    self.dragulaInstance.on('drag', function(el, source) {
                        a(el).addClass('gu-transit-optimized');
                        a('body').addClass('dragging-leads');

                        // Disable text selection during drag
                        a('body').css('user-select', 'none');
                    });

                    self.dragulaInstance.on('dragend', function(el) {
                        a(el).removeClass('gu-transit-optimized');
                        a('body').removeClass('dragging-leads');

                        // Re-enable text selection
                        a('body').css('user-select', '');
                    });

                    self.dragulaInstance.on('over', function(el, container, source) {
                        a(container).addClass('drag-over-highlight');
                    });

                    self.dragulaInstance.on('out', function(el, container, source) {
                        a(container).removeClass('drag-over-highlight');
                    });

                    self.dragulaInstance.on('drop', function(el, target, source, sibling) {
                        // Clear any existing timeout
                        if (self.dragTimeout) {
                            clearTimeout(self.dragTimeout);
                        }

                        // Remove drag classes immediately for better UX
                        a(el).removeClass('gu-transit-optimized');
                        a(target).removeClass('drag-over-highlight');
                        a('body').removeClass('dragging-leads');
                        a('body').css('user-select', '');

                        // Get data efficiently
                        var leadId = a(el).attr('data-id');
                        var sourceId = a(source).attr('id');
                        var targetId = a(target).attr('id');
                        var stageId = a(target).attr('data-id');
                        var oldStatus = a(source).data('status');
                        var newStatus = a(target).data('status');
                        var pipelineId = '<?php echo e($pipeline->id); ?>';

                        // Update counts immediately for better UX
                        var sourceCount = a("#" + sourceId + " > div").length;
                        var targetCount = a("#" + targetId + " > div").length;

                        a("#" + sourceId).parent().find('.count').text(sourceCount);
                        a("#" + targetId).parent().find('.count').text(targetCount);

                        // Collect order efficiently
                        var order = [];
                        a("#" + targetId + " > div").each(function(index) {
                            var dataId = a(this).attr('data-id');
                            if (dataId) order.push(dataId);
                        });

                        // Add loading state
                        a(el).addClass('updating-lead');

                        // Debounced AJAX call
                        self.dragTimeout = setTimeout(function() {
                            a.ajax({
                                url: '<?php echo e(route('lead_stages.moveLeadToStage')); ?>',
                                type: 'POST',
                                data: {
                                    lead_id: leadId,
                                    stage_id: stageId,
                                    order: order,
                                    new_status: newStatus,
                                    old_status: oldStatus,
                                    pipeline_id: pipelineId,
                                    "_token": a('meta[name="csrf-token"]').attr('content')
                                },
                                success: function(data) {
                                    a(el).removeClass('updating-lead');
                                    if (data.status === 'success') {
                                        show_toastr('success', data.message, 'success');
                                    } else {
                                        show_toastr(data.status, data.message, data.status);
                                    }
                                },
                                error: function(xhr) {
                                    a(el).removeClass('updating-lead');
                                    var data = xhr.responseJSON || {};
                                    show_toastr('error', data.message || 'An error occurred', 'error');

                                    // Revert the move on error
                                    if (source !== target) {
                                        a(source).append(el);
                                        // Restore counts
                                        a("#" + sourceId).parent().find('.count').text(a("#" + sourceId + " > div").length);
                                        a("#" + targetId).parent().find('.count').text(a("#" + targetId + " > div").length);
                                    }
                                }
                            });
                        }, 100); // Small delay to prevent rapid fire requests
                    });
                });
            };

            a.Dragula = new t;
            a.Dragula.Constructor = t;
        }(window.jQuery),
        function(a) {
            "use strict";

            // Initialize dragula with proper timing
            a(document).ready(function() {
                // Small delay to ensure DOM is fully rendered
                setTimeout(function() {
                    a.Dragula.init();
                }, 100);
            });

            // Reinitialize on dynamic content updates
            a(document).on('contentUpdated', function() {
                setTimeout(function() {
                    a.Dragula.init();
                }, 100);
            });

        }(window.jQuery);

        function openSourcesModal(sources) {
            const sourcesList = document.getElementById('sources-list');
            sourcesList.innerHTML = ''; // Clear previous sources

            if (sources && sources.length > 0) {
                sources.forEach(source => {
                    const listItem = document.createElement('li');
                    listItem.className = 'list-group-item';
                    listItem.textContent = source.name;
                    sourcesList.appendChild(listItem);
                });
            } else {
                const listItem = document.createElement('li');
                listItem.className = 'list-group-item';
                listItem.textContent = '<?php echo e(__('No sources found for this lead.')); ?>';
                sourcesList.appendChild(listItem);
            }

            const modal = new bootstrap.Modal(document.getElementById('sources-modal'));
            modal.show();
        }
    </script>
    <script>
        $(document).on("change", "#default_pipeline_id", function() {
            $('#change-pipeline').submit();
        });
        
        // Search functionality for leads
        $('#lead-search').on('input', function() {
            var searchTerm = $(this).val().toLowerCase();
            
            // Show/hide clear icon based on input value
            if (searchTerm.length > 0) {
                $('.search-icon').hide();
                $('.clear-icon').show();
            } else {
                $('.search-icon').show();
                $('.clear-icon').hide();
            }
            
            // Search through all lead cards
            $('.kanban-card').each(function() {
                var leadCard = $(this);
                var leadName = leadCard.find('.lead-title a').text().toLowerCase();
                var leadEmail = leadCard.find('.contact-item:nth-child(3) span').text().toLowerCase();
                var leadPhone = leadCard.find('.contact-item:nth-child(2) span').text().toLowerCase();
                
                // Check if search term matches any lead data
                var isMatch = leadName.includes(searchTerm) || 
                             leadEmail.includes(searchTerm) || 
                             leadPhone.includes(searchTerm);
                
                if (searchTerm === '' || isMatch) {
                    leadCard.show();
                } else {
                    leadCard.hide();
                }
            });
            
            // Update stage counts
            $('.kanban-col').each(function() {
                var stageColumn = $(this);
                var visibleLeads = stageColumn.find('.kanban-card:visible').length;
                stageColumn.find('.count').text(visibleLeads);
            });
        });
        
        // Search icon click handler
        $('.search-icon').on('click', function() {
            $('#lead-search').focus();
        });
        
        // Clear icon click handler
        $('.clear-icon').on('click', function() {
            $('#lead-search').val('').trigger('input');
            $('#lead-search').focus();
        });







        // Handle Create Label Modal
        $('#createLabelModal').on('show.bs.modal', function (event) {
            var button = $(event.relatedTarget);
            var leadId = button.data('lead-id');
            var pipelineId = button.data('pipeline-id');

            var modal = $(this);
            modal.find('#modal_lead_id').val(leadId);
            modal.find('#modal_pipeline_id').val(pipelineId);
        });

        // Handle Create Label Form Submission
        $('#createLabelForm').on('submit', function(e) {
            e.preventDefault();

            var formData = $(this).serialize();
            var leadId = $('#modal_lead_id').val();

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    $('#createLabelModal').modal('hide');
                    show_toastr('success', response.message || 'Label created successfully!');

                    // Reload the page to show the new label
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                },
                error: function(xhr) {
                    var errorMessage = 'An error occurred';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                        var errors = xhr.responseJSON.errors;
                        errorMessage = Object.values(errors).flat().join(', ');
                    }
                    show_toastr('error', errorMessage);
                }
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Lead')); ?></li>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('action-btn'); ?>
    <div class="d-flex flex-column flex-md-row align-items-stretch align-items-md-center justify-content-md-end gap-3">
        <!-- Search Bar -->
        <div class="search-container mb-3 mb-md-0">
            <input type="text" 
                   id="lead-search" 
                   class="form-control search-input" 
                   placeholder="<?php echo e(__('Search leads...')); ?>" 
                   autocomplete="off">
            <i class="fas fa-search search-icon"></i>
            <i class="fas fa-times clear-icon" style="display: none;"></i>
        </div>
        
        <!-- Pipeline Select -->
        <?php echo e(Form::open(['route' => 'deals.change.pipeline', 'id' => 'change-pipeline', 'class' => 'mb-3 mb-md-0'])); ?>

        <?php echo e(Form::select('default_pipeline_id', $pipelines, $pipeline->id, ['class' => 'form-control select', 'id' => 'default_pipeline_id'])); ?>

        <?php echo e(Form::close()); ?>


        <!-- Action Buttons Row -->
        <div class="action-buttons-row">
            <!-- List View -->
            <a href="<?php echo e(route('leads.list')); ?>"
                data-size="lg"
                data-ajax-popup="true"
                data-bs-toggle="tooltip"
                data-bs-placement="bottom"
                title="<?php echo e(__('List View')); ?>"
                class="action-btn"
                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                        border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                onmouseover="this.style.transform='scale(1.1)'"
                onmouseout="this.style.transform='scale(1)'">
                <i class="ti ti-list" style="font-size:16px;"></i>
            </a>

            <!-- Import Lead -->
            <a href="#"
                data-size="md"
                data-bs-toggle="tooltip"
                data-ajax-popup="true"
                data-bs-placement="bottom"
                title="<?php echo e(__('Import')); ?>"
                data-url="<?php echo e(route('leads.import')); ?>"
                data-ajax-popup="true"
                data-title="<?php echo e(__('Import Lead CSV file')); ?>"
                class="action-btn"
                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                        border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                onmouseover="this.style.transform='scale(1.1)'"
                onmouseout="this.style.transform='scale(1)'">
                <i class="fas fa-file-import" style="font-size:16px;"></i>
            </a>

            <!-- Filter Button -->
            <button id="openFilterPanel"
                data-bs-toggle="tooltip"
                data-bs-placement="bottom"
                title="<?php echo e(__('Filter')); ?>"
                class="action-btn"
                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                        border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;border:none;"
                onmouseover="this.style.transform='scale(1.1)'"
                onmouseout="this.style.transform='scale(1)'">
                <i class="fas fa-filter" style="font-size:16px;"></i>
            </button>

            <!-- Export Leads -->
            <a href="<?php echo e(route('leads.export')); ?>"
                data-bs-toggle="tooltip"
                data-bs-placement="bottom"
                title="<?php echo e(__('Export')); ?>"
                class="action-btn"
                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                        border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                onmouseover="this.style.transform='scale(1.1)'"
                onmouseout="this.style.transform='scale(1)'">
                <i class="fas fa-file-export" style="font-size:16px;"></i>
            </a>

            <!-- Create New Lead -->
            <a href="#"
                data-size="lg"
                data-url="<?php echo e(route('leads.create')); ?>"
                data-ajax-popup="true"
                data-bs-toggle="tooltip"
                data-bs-placement="bottom"
                title="<?php echo e(__('Create New Lead')); ?>"
                data-title="<?php echo e(__('Create Lead')); ?>"
                class="action-btn"
                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                        border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                onmouseover="this.style.transform='scale(1.1)'"
                onmouseout="this.style.transform='scale(1)'">
                <i class="ti ti-plus" style="font-size:16px;"></i>
            </a>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-sm-12">
            <?php
                $lead_stages = $pipeline->leadStages;
                $json = [];
                foreach ($lead_stages as $lead_stage) {
                    $json[] = 'task-list-' . $lead_stage->id;
                }
            ?>
            <div class="kanban-wrapper horizontal-scroll-cards" data-containers='<?php echo json_encode($json); ?>' data-plugin="dragula">
                <?php $__currentLoopData = $lead_stages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lead_stage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php ($leads = $lead_stage->lead()); ?>
                    <div class="kanban-col crm-sales-card">
                        <div class="kanban-header">
                            <h4><?php echo e($lead_stage->name); ?></h4>
                            <span class="count f-w-600"><?php echo e(count($leads)); ?></span>
                        </div>
                        <div class="sales-item-wrp" id="task-list-<?php echo e($lead_stage->id); ?>" data-id="<?php echo e($lead_stage->id); ?>" data-status="<?php echo e($lead_stage->id); ?>">
                            <?php $__currentLoopData = $leads; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lead): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="kanban-card sales-item" data-id="<?php echo e($lead->id); ?>" style="border-top: 4px solid #2e7d32;">
                                    <div class="card-top">
                                        <div class="lead-title">
                                            <a href="<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view lead')): ?><?php if($lead->is_active): ?><?php echo e(route('leads.show', $lead->id)); ?><?php else: ?>#<?php endif; ?> <?php else: ?>#<?php endif; ?>" class="dashboard-link">
                                            <i class="fas fa-user me-2"></i><?php echo e($lead->name); ?>

                                            </a>
                                        </div>
                                        <?php if(Auth::user()->type != 'client'): ?>
                                            <div class="btn-group card-option">
                                                <button type="button" class="btn p-0 border-0" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="ti ti-dots-vertical"></i>
                                                </button>
                                                <div class="dropdown-menu icon-dropdown dropdown-menu-end">
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit lead')): ?>
                                                        <a href="#" data-size="md" data-url="<?php echo e(URL::to('leads/' . $lead->id . '/labels')); ?>" data-ajax-popup="true" class="dropdown-item" data-bs-original-title="<?php echo e(__('Add Labels')); ?>">
                                                            <i class="ti ti-bookmark"></i>
                                                            <span><?php echo e(__('Labels')); ?></span>
                                                        </a>
                                                        <a href="#" data-size="lg" data-url="<?php echo e(URL::to('leads/' . $lead->id . '/edit')); ?>" data-ajax-popup="true" class="dropdown-item" data-bs-original-title="<?php echo e(__('Edit Lead')); ?>">
                                                            <i class="ti ti-pencil"></i>
                                                            <span><?php echo e(__('Edit')); ?></span>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete lead')): ?>
                                                        <?php echo Form::open([
                                                            'method' => 'DELETE',
                                                            'route' => ['leads.destroy', $lead->id],
                                                            'id' => 'delete-form-' . $lead->id,
                                                        ]); ?>

                                                        <a href="#" class="dropdown-item bs-pass-para">
                                                            <i class="ti ti-trash"></i>
                                                            <span> <?php echo e(__('Delete')); ?> </span>
                                                        </a>
                                                        <?php echo Form::close(); ?>

                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="badge-wrp">
                                        <?php ($tags = $lead->tags()); ?>
                                        <?php if($tags): ?>
                                            <?php $__currentLoopData = $tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="kanban-label bg-light-<?php echo e($tag->color); ?>"><?php echo e($tag->name); ?></span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                    </div>
                                    <?php
                                    $products = $lead->products();
                                    $sources = $lead->sources();
                                    ?>
                                    <div class="contact-info position-relative">
                                        <button type="button" class="position-absolute top-0 end-0 d-inline-flex align-items-center gap-1 p-1 px-2 border rounded-1 bg-light" style="margin-right: 17px;" data-bs-toggle="tooltip" title="<?php echo e(__('Source')); ?>" onclick="openSourcesModal(<?php echo e(json_encode($sources)); ?>)">
                                            <i class="f-16 ti ti-social"></i>
                                            <!-- <?php echo e(count($sources)); ?> -->
                                        </button>
                                        <div class="contact-item text-success">
                                            <i class="fas fa-phone-volume me-1"></i>
                                            <span><?php echo e($lead->phone); ?></span>
                                        </div>
                                        <div class="contact-item text-success">
                                            <i class="fas fa-envelope me-1"></i>
                                            <span><?php echo e($lead->email); ?></span>
                                        </div>
                                    </div>
                                    <div class="card-bottom">
                                        <div class="communication-buttons">
                                            <button class="communication-btn call" data-bs-toggle="tooltip" title="Call">
                                                <i class="fas fa-phone-alt"></i>
                                            </button>
                                            <button class="communication-btn whatsapp" data-bs-toggle="tooltip" title="WhatsApp">
                                                <i class="fab fa-whatsapp"></i>
                                            </button>
                                            <button class="communication-btn email" data-bs-toggle="tooltip" title="Email" onclick="openCommunicationModal(<?php echo e(json_encode(['name' => $lead->name, 'phone' => $lead->phone, 'email' => $lead->email])); ?>)">
                                                <i class="fas fa-envelope"></i>
                                            </button>
                                            <button class="communication-btn activity" data-bs-toggle="modal" data-bs-target="#activityModal-<?php echo e($lead->id); ?>" title="Activity">
                                                <i class="fas fa-stream"></i>
                                            </button>
                                        </div>
                                        <div class="user-group">
                                            <?php $__currentLoopData = $lead->users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <i class="fas fa-user-circle" data-bs-toggle="tooltip" title="<?php echo e($user->name); ?>"></i>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>

<!-- Communication Modal -->
<div class="modal fade" id="communication-modal" tabindex="-1" aria-labelledby="communication-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-comm-modal">
            <div class="modal-header">
                <div class="d-flex align-items-center gap-3">
                    <div id="comm-lead-avatar" class="comm-lead-avatar"></div>
                    <div>
                        <h5 class="modal-title mb-0" id="communication-modal-label"></h5>
                        <div class="comm-lead-name" id="comm-lead-name"></div>
                        <div class="comm-lead-contact text-muted small" id="comm-lead-contact"></div>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="communication-options-list">
                    <a href="#" class="communication-option-item" id="default-email-option">
                        <i class="fas fa-envelope-open-text"></i>
                        <span>Default Email App</span>
                    </a>
                    <a href="#" class="communication-option-item" id="cloud-email-option">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <span>Cloud Email Service</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sources Modal -->
<div class="modal fade" id="sources-modal" tabindex="-1" aria-labelledby="sources-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sources-modal-label"><?php echo e(__('Lead Sources')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <ul class="list-group" id="sources-list">
                    <!-- Sources will be dynamically inserted here -->
                </ul>
            </div>
        </div>
    </div>
</div>

<?php $__currentLoopData = $lead_stages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lead_stage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php ($leads = $lead_stage->lead()); ?>
    <?php $__currentLoopData = $leads; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lead): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <!-- Activity Modal for this lead -->
        <div class="modal fade" id="activityModal-<?php echo e($lead->id); ?>" tabindex="-1" aria-labelledby="activityModalLabel-<?php echo e($lead->id); ?>" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="activityModalLabel-<?php echo e($lead->id); ?>"><?php echo e(__('Activity')); ?></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row leads-scroll">
                            <ul class="event-cards list-group list-group-flush mt-3 w-100">
                                <?php if(!$lead->activities->isEmpty()): ?>
                                    <?php $__currentLoopData = $lead->activities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li class="list-group-item card mb-3">
                                            <div class="row align-items-center justify-content-between">
                                                <div class="col-auto mb-3 mb-sm-0">
                                                    <div class="d-flex align-items-center">
                                                        <div class="theme-avtar bg-primary badge">
                                                            <i class="ti <?php echo e($activity->logIcon()); ?>"></i>
                                                        </div>
                                                        <div class="ms-3">
                                                            <span class="text-dark text-sm"><?php echo e(__($activity->log_type)); ?></span>
                                                            <h6 class="m-0"><?php echo $activity->getLeadRemark(); ?></h6>
                                                            <small class="text-muted"><?php echo e($activity->created_at->diffForHumans()); ?></small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <li class="text-center py-4">No activity found yet.</li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>







<!-- Filter Panel -->
<div class="filter-overlay" id="filterOverlay"></div>
<div id="filterPanel" class="filter-panel">
    <!-- Main Filter Categories View -->
    <div id="filterMainView" class="filter-view">
        <div class="filter-header">
            <div class="header-left">
                <i class="fas fa-filter filter-icon"></i>
                <h4><?php echo e(__('Filters')); ?></h4>
            </div>
            <button id="closeFilterPanel" class="close-btn">&times;</button>
        </div>
        <div class="filter-content">
            <div class="filter-categories">
                <div class="filter-category-item" id="assignedToCategory">
                    <div class="category-left">
                        <i class="fas fa-user category-icon"></i>
                        <span class="category-name"><?php echo e(__('Assigned to')); ?></span>
                    </div>
                    <i class="fas fa-chevron-right category-arrow"></i>
                </div>
                <div class="filter-category-item" id="filterByTagCategory">
                    <div class="category-left">
                        <i class="fas fa-tags category-icon"></i>
                        <span class="category-name"><?php echo e(__('Filter By Tag')); ?></span>
                    </div>
                    <i class="fas fa-chevron-right category-arrow"></i>
                </div>
                <div class="filter-category-item" id="createdOnCategory">
                    <div class="category-left">
                        <i class="fas fa-calendar-alt category-icon"></i>
                        <span class="category-name"><?php echo e(__('Created on')); ?></span>
                    </div>
                    <i class="fas fa-chevron-right category-arrow"></i>
                </div>
                <div class="filter-category-item" id="updatedOnCategory">
                    <div class="category-left">
                        <i class="fas fa-calendar-check category-icon"></i>
                        <span class="category-name"><?php echo e(__('Updated on')); ?></span>
                    </div>
                    <i class="fas fa-chevron-right category-arrow"></i>
                </div>
                <div class="filter-category-item" id="leadSourceCategory">
                    <div class="category-left">
                        <i class="fas fa-map-marker-alt category-icon"></i>
                        <span class="category-name"><?php echo e(__('Lead source')); ?></span>
                    </div>
                    <i class="fas fa-chevron-right category-arrow"></i>
                </div>
                <!-- Add more filter categories here in the future -->
            </div>
        </div>
        <div class="filter-footer">
            <button id="clearAllMainFilters" class="btn btn-link"><?php echo e(__('Clear all')); ?></button>
            <div>
                <button id="cancelMainFilter" class="btn btn-secondary me-2"><?php echo e(__('Cancel')); ?></button>
            </div>
        </div>
    </div>

    <!-- Assigned To Detail View -->
    <div id="filterAssignedToView" class="filter-view" style="display: none;">
        <div class="filter-header">
            <div class="header-left">
                <button id="backToMainFilter" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <i class="fas fa-filter filter-icon"></i>
                <h4><?php echo e(__('Assigned to')); ?></h4>
            </div>
            <button id="closeFilterPanel2" class="close-btn">&times;</button>
        </div>
        <div class="filter-content">
            <div class="filter-section">
                <input type="text" id="userSearch" placeholder="<?php echo e(__('Search')); ?>" class="user-search" />
                <div id="userList" class="user-list">
                    <!-- Users will be loaded dynamically -->
                </div>
            </div>
        </div>
        <div class="filter-footer">
            <button id="clearAllFilters" class="btn btn-link"><?php echo e(__('Clear all')); ?></button>
            <div>
                <button id="cancelFilter" class="btn btn-secondary me-2"><?php echo e(__('Cancel')); ?></button>
                <button id="applyFilter" class="btn btn-success"><?php echo e(__('Apply')); ?></button>
            </div>
        </div>
    </div>

    <!-- Filter By Tag Detail View -->
    <div id="filterByTagView" class="filter-view" style="display: none;">
        <div class="filter-header">
            <div class="header-left">
                <button id="backToMainFilterFromTag" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <i class="fas fa-filter filter-icon"></i>
                <h4><?php echo e(__('Filter By Tag')); ?></h4>
            </div>
            <button id="closeFilterPanel3" class="close-btn">&times;</button>
        </div>
        <div class="filter-content">
            <div class="filter-section">
                <input type="text" id="tagSearch" placeholder="<?php echo e(__('Search')); ?>" class="user-search" />
                <div id="tagList" class="user-list">
                    <!-- Tags will be loaded dynamically -->
                </div>
            </div>
        </div>
        <div class="filter-footer">
            <button id="clearAllTagFilters" class="btn btn-link"><?php echo e(__('Clear all')); ?></button>
            <div>
                <button id="cancelTagFilter" class="btn btn-secondary me-2"><?php echo e(__('Cancel')); ?></button>
                <button id="applyTagFilter" class="btn btn-success"><?php echo e(__('Apply')); ?></button>
            </div>
        </div>
    </div>

    <!-- Created On Detail View -->
    <div id="createdOnView" class="filter-view" style="display: none;">
        <div class="filter-header">
            <div class="header-left">
                <button id="backToMainFilterFromCreatedDate" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <i class="fas fa-filter filter-icon"></i>
                <h4><?php echo e(__('Created on')); ?></h4>
            </div>
            <button id="closeFilterPanel4" class="close-btn">&times;</button>
        </div>
        <div class="filter-content">
            <div class="filter-section">
                <div id="createdDateRangeList" class="user-list">
                    <div class="user-item date-option" data-range="today">
                        <input type="radio" name="dateRange" value="today" id="date_today">
                        <label for="date_today"><?php echo e(__('Today')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="yesterday">
                        <input type="radio" name="dateRange" value="yesterday" id="date_yesterday">
                        <label for="date_yesterday"><?php echo e(__('Yesterday')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="this_week">
                        <input type="radio" name="dateRange" value="this_week" id="date_this_week">
                        <label for="date_this_week"><?php echo e(__('This Week')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="last_week">
                        <input type="radio" name="dateRange" value="last_week" id="date_last_week">
                        <label for="date_last_week"><?php echo e(__('Last Week')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="last_7_days">
                        <input type="radio" name="dateRange" value="last_7_days" id="date_last_7_days">
                        <label for="date_last_7_days"><?php echo e(__('Last 7 Days')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="last_30_days">
                        <input type="radio" name="dateRange" value="last_30_days" id="date_last_30_days">
                        <label for="date_last_30_days"><?php echo e(__('Last 30 Days')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="this_month">
                        <input type="radio" name="dateRange" value="this_month" id="date_this_month">
                        <label for="date_this_month"><?php echo e(__('This Month')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="last_month">
                        <input type="radio" name="dateRange" value="last_month" id="date_last_month">
                        <label for="date_last_month"><?php echo e(__('Last Month')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="this_year">
                        <input type="radio" name="dateRange" value="this_year" id="date_this_year">
                        <label for="date_this_year"><?php echo e(__('This Year')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="last_year">
                        <input type="radio" name="dateRange" value="last_year" id="date_last_year">
                        <label for="date_last_year"><?php echo e(__('Last Year')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="custom">
                        <input type="radio" name="dateRange" value="custom" id="date_custom">
                        <label for="date_custom"><?php echo e(__('Custom')); ?></label>
                    </div>
                </div>
                
                <!-- Custom Date Range Picker -->
                <div id="customCreatedDateRange" style="display: none; margin-top: 16px;">
                    <div class="date-range-picker">
                        <input type="date" id="createdStartDate" class="form-control date-input" placeholder="<?php echo e(__('Start Date')); ?>">
                        <span class="date-separator">-</span>
                        <input type="date" id="createdEndDate" class="form-control date-input" placeholder="<?php echo e(__('End Date')); ?>">
                    </div>
                </div>
            </div>
        </div>
        <div class="filter-footer">
            <button id="clearCreatedDateFilter" class="btn btn-link"><?php echo e(__('Clear all')); ?></button>
            <div>
                <button id="cancelCreatedDateFilter" class="btn btn-secondary me-2"><?php echo e(__('Cancel')); ?></button>
                <button id="applyCreatedDateFilter" class="btn btn-success"><?php echo e(__('Apply')); ?></button>
            </div>
        </div>
    </div>

    <!-- Updated On Detail View -->
    <div id="updatedOnView" class="filter-view" style="display: none;">
        <div class="filter-header">
            <div class="header-left">
                <button id="backToMainFilterFromUpdatedDate" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <i class="fas fa-filter filter-icon"></i>
                <h4><?php echo e(__('Updated on')); ?></h4>
            </div>
            <button id="closeFilterPanel5" class="close-btn">&times;</button>
        </div>
        <div class="filter-content">
            <div class="filter-section">
                <div id="updatedDateRangeList" class="user-list">
                    <div class="user-item date-option" data-range="today">
                        <input type="radio" name="updatedDateRange" value="today" id="updated_date_today">
                        <label for="updated_date_today"><?php echo e(__('Today')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="yesterday">
                        <input type="radio" name="updatedDateRange" value="yesterday" id="updated_date_yesterday">
                        <label for="updated_date_yesterday"><?php echo e(__('Yesterday')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="this_week">
                        <input type="radio" name="updatedDateRange" value="this_week" id="updated_date_this_week">
                        <label for="updated_date_this_week"><?php echo e(__('This Week')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="last_week">
                        <input type="radio" name="updatedDateRange" value="last_week" id="updated_date_last_week">
                        <label for="updated_date_last_week"><?php echo e(__('Last Week')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="last_7_days">
                        <input type="radio" name="updatedDateRange" value="last_7_days" id="updated_date_last_7_days">
                        <label for="updated_date_last_7_days"><?php echo e(__('Last 7 Days')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="last_30_days">
                        <input type="radio" name="updatedDateRange" value="last_30_days" id="updated_date_last_30_days">
                        <label for="updated_date_last_30_days"><?php echo e(__('Last 30 Days')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="this_month">
                        <input type="radio" name="updatedDateRange" value="this_month" id="updated_date_this_month">
                        <label for="updated_date_this_month"><?php echo e(__('This Month')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="last_month">
                        <input type="radio" name="updatedDateRange" value="last_month" id="updated_date_last_month">
                        <label for="updated_date_last_month"><?php echo e(__('Last Month')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="this_year">
                        <input type="radio" name="updatedDateRange" value="this_year" id="updated_date_this_year">
                        <label for="updated_date_this_year"><?php echo e(__('This Year')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="last_year">
                        <input type="radio" name="updatedDateRange" value="last_year" id="updated_date_last_year">
                        <label for="updated_date_last_year"><?php echo e(__('Last Year')); ?></label>
                    </div>
                    <div class="user-item date-option" data-range="custom">
                        <input type="radio" name="updatedDateRange" value="custom" id="updated_date_custom">
                        <label for="updated_date_custom"><?php echo e(__('Custom')); ?></label>
                    </div>
                </div>
                
                <!-- Custom Date Range Picker -->
                <div id="customUpdatedDateRange" style="display: none; margin-top: 16px;">
                    <div class="date-range-picker">
                        <input type="date" id="updatedStartDate" class="form-control date-input" placeholder="<?php echo e(__('Start Date')); ?>">
                        <span class="date-separator">-</span>
                        <input type="date" id="updatedEndDate" class="form-control date-input" placeholder="<?php echo e(__('End Date')); ?>">
                    </div>
                </div>
            </div>
        </div>
        <div class="filter-footer">
            <button id="clearUpdatedDateFilter" class="btn btn-link"><?php echo e(__('Clear all')); ?></button>
            <div>
                <button id="cancelUpdatedDateFilter" class="btn btn-secondary me-2"><?php echo e(__('Cancel')); ?></button>
                <button id="applyUpdatedDateFilter" class="btn btn-success"><?php echo e(__('Apply')); ?></button>
            </div>
        </div>
    </div>

    <!-- Lead Source Detail View -->
    <div id="leadSourceView" class="filter-view" style="display: none;">
        <div class="filter-header">
            <div class="header-left">
                <button id="backToMainFilterFromSource" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <i class="fas fa-filter filter-icon"></i>
                <h4><?php echo e(__('Lead source')); ?></h4>
            </div>
            <button id="closeFilterPanel6" class="close-btn">&times;</button>
        </div>
        <div class="filter-content">
            <div class="filter-section">
                <input type="text" id="sourceSearch" placeholder="<?php echo e(__('Search')); ?>" class="user-search" />
                <div id="sourceList" class="user-list">
                    <!-- Sources will be loaded dynamically -->
                </div>
            </div>
        </div>
        <div class="filter-footer">
            <button id="clearAllSourceFilters" class="btn btn-link"><?php echo e(__('Clear all')); ?></button>
            <div>
                <button id="cancelSourceFilter" class="btn btn-secondary me-2"><?php echo e(__('Cancel')); ?></button>
                <button id="applySourceFilter" class="btn btn-success"><?php echo e(__('Apply')); ?></button>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<script>
    // Modern openCommunicationModal function
    function openCommunicationModal(lead) {
        const modalEl = document.getElementById('communication-modal');
        const modal = new bootstrap.Modal(modalEl);
        const title = document.getElementById('communication-modal-label');
        const name = document.getElementById('comm-lead-name');
        const contact = document.getElementById('comm-lead-contact');
        const avatar = document.getElementById('comm-lead-avatar');

        // Set modal title
        title.textContent = 'Contact Lead';
        name.textContent = lead.name || '';
        contact.innerHTML =
            (lead.phone ? `<i class='fas fa-phone-alt'></i> ${lead.phone}` : '') +
            (lead.email ? ` &nbsp; <i class='fas fa-envelope'></i> ${lead.email}` : '');
        // Avatar: initials
        if (lead.name) {
            const initials = lead.name.split(' ').map(w => w[0]).join('').substring(0,2).toUpperCase();
            avatar.textContent = initials;
        } else {
            avatar.textContent = '?';
        }
        // WhatsApp
        // const whatsapp = document.getElementById('whatsapp-option');
        // if (lead.phone) {
        //     whatsapp.href = `https://wa.me/${lead.phone.replace(/[^\d]/g, '')}`;
        //     whatsapp.classList.remove('disabled');
        // } else {
        //     whatsapp.href = '#';
        //     whatsapp.classList.add('disabled');
        // }
        // Email (default app)
        const email = document.getElementById('default-email-option');
        if (lead.email) {
            email.href = `mailto:${lead.email}`;
            email.classList.remove('disabled');
        } else {
            email.href = '#';
            email.classList.add('disabled');
        }
        // Cloud email (customize as needed)
        const cloud = document.getElementById('cloud-email-option');
        if (lead.email) {
            cloud.href = `mailto:${lead.email}`;
            cloud.classList.remove('disabled');
        } else {
            cloud.href = '#';
            cloud.classList.add('disabled');
        }
        // SMS
        // const sms = document.getElementById('sms-option');
        // if (lead.phone) {
        //     sms.href = `sms:${lead.phone}`;
        //     sms.classList.remove('disabled');
        // } else {
        //     sms.href = '#';
        //     sms.classList.add('disabled');
        // }
        modal.show();
    }
            // Attach openCommunicationModal to each card's SMS and Email button using event delegation
        document.addEventListener('DOMContentLoaded', function() {
            document.body.addEventListener('click', function(e) {
                // Email button
                if (e.target.closest('.kanban-card .communication-btn.email[onclick^="openCommunicationModal"]')) {
                    e.preventDefault();
                    const btn = e.target.closest('.kanban-card .communication-btn.email');
                    const card = btn.closest('.kanban-card');
                    const name = card.querySelector('.lead-title a').textContent.trim();
                    const phone = card.querySelector('.contact-item:nth-child(2) span')?.textContent.trim();
                    const email = card.querySelector('.contact-item:nth-child(3) span')?.textContent.trim();
                    openCommunicationModal({name, phone, email});
                }
                // Call button
                if (e.target.closest('.kanban-card .communication-btn.call')) {
                    e.preventDefault();
                    const btn = e.target.closest('.kanban-card .communication-btn.call');
                    const card = btn.closest('.kanban-card');
                    const name = card.querySelector('.lead-title a').textContent.trim();
                    const phone = card.querySelector('.contact-item:nth-child(2) span')?.textContent.trim();
                    
                    // Open phone dialer if phone number exists
                    if (phone) {
                        const cleanPhone = phone.replace(/[^\d]/g, '');
                        const callUrl = `tel:${cleanPhone}`;
                        window.open(callUrl, '_self');
                    } else {
                        show_toastr('error', 'No phone number available for call', 'error');
                    }
                }
                
                // WhatsApp button
                if (e.target.closest('.kanban-card .communication-btn.whatsapp')) {
                    e.preventDefault();
                    const btn = e.target.closest('.kanban-card .communication-btn.whatsapp');
                    const card = btn.closest('.kanban-card');
                    const name = card.querySelector('.lead-title a').textContent.trim();
                    const phone = card.querySelector('.contact-item:nth-child(2) span')?.textContent.trim();
                    
                    // Open WhatsApp directly if phone number exists
                    if (phone) {
                        const cleanPhone = phone.replace(/[^\d]/g, '');
                        const whatsappUrl = `https://wa.me/${cleanPhone}?text=Hi ${name}, I hope you're doing well. I'd like to discuss a business opportunity with you.`;
                        window.open(whatsappUrl, '_blank');
                    } else {
                        show_toastr('error', 'No phone number available for WhatsApp', 'error');
                    }
                }
            });

        // --- Modal backdrop/blur fix ---
        // Remove any leftover modal-backdrop and reset body styles when any modal is hidden
        document.querySelectorAll('.modal').forEach(function(modal) {
            modal.addEventListener('hidden.bs.modal', function () {
                // Remove all modal-backdrop elements
                document.querySelectorAll('.modal-backdrop').forEach(function(backdrop) {
                    backdrop.parentNode.removeChild(backdrop);
                });
                // Remove modal-open class from body
                document.body.classList.remove('modal-open');
                // Remove any inline style added by Bootstrap
                document.body.style = '';
            });
        });

        // ===== FILTER PANEL LOGIC =====
        let allUsers = [];
        let allTags = [];
        let allSources = [];
        let activeFilters = {
            assigned_to: [],
            tags: [],
            created_on: {
                range: '',
                start_date: '',
                end_date: ''
            },
            updated_on: {
                range: '',
                start_date: '',
                end_date: ''
            },
            sources: []
        };

        // Load users, tags, and sources when page loads
        loadUsers();
        loadTags();
        loadSources();

        // Open filter panel
        $('#openFilterPanel').on('click', function() {
            $('#filterPanel').addClass('open');
            $('#filterOverlay').addClass('show');
            $('body').css('overflow', 'hidden');
        });

        // Close filter panel
        $('#closeFilterPanel, #closeFilterPanel2, #closeFilterPanel3, #closeFilterPanel4, #closeFilterPanel5, #closeFilterPanel6, #filterOverlay, #cancelFilter, #cancelTagFilter, #cancelCreatedDateFilter, #cancelUpdatedDateFilter, #cancelSourceFilter, #cancelMainFilter').on('click', function() {
            closeFilterPanel();
        });

        // Navigate to Assigned To filter
        $('#assignedToCategory').on('click', function() {
            showAssignedToView();
        });

        // Navigate to Tag filter
        $('#filterByTagCategory').on('click', function() {
            showTagFilterView();
        });

        // Navigate to Created On filter
        $('#createdOnCategory').on('click', function() {
            showCreatedOnView();
        });

        // Navigate to Updated On filter
        $('#updatedOnCategory').on('click', function() {
            showUpdatedOnView();
        });

        // Navigate to Lead Source filter
        $('#leadSourceCategory').on('click', function() {
            showLeadSourceView();
        });

        // Back to main filter view
        $('#backToMainFilter, #backToMainFilterFromTag, #backToMainFilterFromCreatedDate, #backToMainFilterFromUpdatedDate, #backToMainFilterFromSource').on('click', function() {
            showMainFilterView();
        });

        function showMainFilterView() {
            $('#filterAssignedToView').hide();
            $('#filterByTagView').hide();
            $('#createdOnView').hide();
            $('#updatedOnView').hide();
            $('#leadSourceView').hide();
            $('#filterMainView').show();
        }

        function showAssignedToView() {
            $('#filterMainView').hide();
            $('#filterByTagView').hide();
            $('#createdOnView').hide();
            $('#updatedOnView').hide();
            $('#leadSourceView').hide();
            $('#filterAssignedToView').show();
        }

        function showTagFilterView() {
            $('#filterMainView').hide();
            $('#filterAssignedToView').hide();
            $('#createdOnView').hide();
            $('#updatedOnView').hide();
            $('#leadSourceView').hide();
            $('#filterByTagView').show();
        }

        function showCreatedOnView() {
            $('#filterMainView').hide();
            $('#filterAssignedToView').hide();
            $('#filterByTagView').hide();
            $('#updatedOnView').hide();
            $('#leadSourceView').hide();
            $('#createdOnView').show();
        }

        function showUpdatedOnView() {
            $('#filterMainView').hide();
            $('#filterAssignedToView').hide();
            $('#filterByTagView').hide();
            $('#createdOnView').hide();
            $('#leadSourceView').hide();
            $('#updatedOnView').show();
        }

        function showLeadSourceView() {
            $('#filterMainView').hide();
            $('#filterAssignedToView').hide();
            $('#filterByTagView').hide();
            $('#createdOnView').hide();
            $('#updatedOnView').hide();
            $('#leadSourceView').show();
        }

        function closeFilterPanel() {
            $('#filterPanel').removeClass('open');
            $('#filterOverlay').removeClass('show');
            $('body').css('overflow', '');
            // Reset to main view when closing
            showMainFilterView();
        }

        // Load users for filter
        function loadUsers() {
            $.ajax({
                url: '<?php echo e(route('leads.filter.users')); ?>',
                type: 'GET',
                success: function(response) {
                    allUsers = response.users;
                    renderUserList();
                },
                error: function(xhr) {
                    console.error('Failed to load users:', xhr);
                }
            });
        }

        // Render user list
        function renderUserList(searchTerm = '') {
            const userList = $('#userList');
            userList.empty();

            // Add "Assigned to None" option
            const noneItem = $(`
                <div class="user-item">
                    <input type="checkbox" value="none" id="user_none" ${activeFilters.assigned_to.includes('none') ? 'checked' : ''}>
                    <label for="user_none"><?php echo e(__('Assigned to None')); ?></label>
                </div>
            `);
            userList.append(noneItem);

            // Filter and add users
            const filteredUsers = allUsers.filter(user => 
                user.name.toLowerCase().includes(searchTerm.toLowerCase())
            );

            filteredUsers.forEach(user => {
                const userItem = $(`
                    <div class="user-item">
                        <input type="checkbox" value="${user.id}" id="user_${user.id}" ${activeFilters.assigned_to.includes(user.id.toString()) ? 'checked' : ''}>
                        <label for="user_${user.id}">${user.name}</label>
                    </div>
                `);
                userList.append(userItem);
            });
        }

        // Search users
        $('#userSearch').on('input', function() {
            const searchTerm = $(this).val();
            renderUserList(searchTerm);
        });

        // Search tags
        $('#tagSearch').on('input', function() {
            const searchTerm = $(this).val();
            renderTagList(searchTerm);
        });

        // Search sources
        $('#sourceSearch').on('input', function() {
            const searchTerm = $(this).val();
            renderSourceList(searchTerm);
        });

        // Clear all filters
        $('#clearAllFilters').on('click', function() {
            activeFilters.assigned_to = [];
            renderUserList();
            updateFilterIndicator();
        });

        // Clear all tag filters
        $('#clearAllTagFilters').on('click', function() {
            activeFilters.tags = [];
            renderTagList();
            updateFilterIndicator();
        });

        // Clear created date filter
        $('#clearCreatedDateFilter').on('click', function() {
            activeFilters.created_on = { range: '', start_date: '', end_date: '' };
            $('input[name="dateRange"]').prop('checked', false);
            $('#customCreatedDateRange').hide();
            $('#createdStartDate, #createdEndDate').val('');
            updateFilterIndicator();
        });

        // Clear updated date filter
        $('#clearUpdatedDateFilter').on('click', function() {
            activeFilters.updated_on = { range: '', start_date: '', end_date: '' };
            $('input[name="updatedDateRange"]').prop('checked', false);
            $('#customUpdatedDateRange').hide();
            $('#updatedStartDate, #updatedEndDate').val('');
            updateFilterIndicator();
        });

        // Clear source filter
        $('#clearAllSourceFilters').on('click', function() {
            activeFilters.sources = [];
            renderSourceList();
            updateFilterIndicator();
        });

        // Clear all filters from main view
        $('#clearAllMainFilters').on('click', function() {
            // Reset all active filters
            activeFilters.assigned_to = [];
            activeFilters.tags = [];
            activeFilters.created_on = { range: '', start_date: '', end_date: '' };
            activeFilters.updated_on = { range: '', start_date: '', end_date: '' };
            activeFilters.sources = [];
            
            // Clear all form inputs
            $('#userList input[type="checkbox"]').prop('checked', false);
            $('#tagList input[type="checkbox"]').prop('checked', false);
            $('#sourceList input[type="checkbox"]').prop('checked', false);
            $('input[name="dateRange"]').prop('checked', false);
            $('input[name="updatedDateRange"]').prop('checked', false);
            $('#customCreatedDateRange').hide();
            $('#customUpdatedDateRange').hide();
            $('#createdStartDate, #createdEndDate, #updatedStartDate, #updatedEndDate').val('');
            
            // Update filter indicator and apply changes
            updateFilterIndicator();
            applyFiltersToKanban();
            closeFilterPanel();
        });

        // Handle custom created date range toggle
        $('input[name="dateRange"]').on('change', function() {
            if ($(this).val() === 'custom') {
                $('#customCreatedDateRange').show();
            } else {
                $('#customCreatedDateRange').hide();
                $('#createdStartDate, #createdEndDate').val('');
            }
        });

        // Handle custom updated date range toggle
        $('input[name="updatedDateRange"]').on('change', function() {
            if ($(this).val() === 'custom') {
                $('#customUpdatedDateRange').show();
            } else {
                $('#customUpdatedDateRange').hide();
                $('#updatedStartDate, #updatedEndDate').val('');
            }
        });

        // Apply filters
        $('#applyFilter').on('click', function() {
            // Collect selected users
            const selectedUsers = [];
            $('#userList input[type="checkbox"]:checked').each(function() {
                selectedUsers.push($(this).val());
            });

            activeFilters.assigned_to = selectedUsers;
            updateFilterIndicator();
            applyFiltersToKanban();
            closeFilterPanel();
        });

        // Apply tag filters
        $('#applyTagFilter').on('click', function() {
            // Collect selected tags
            const selectedTags = [];
            $('#tagList input[type="checkbox"]:checked').each(function() {
                selectedTags.push($(this).val());
            });

            activeFilters.tags = selectedTags;
            updateFilterIndicator();
            applyFiltersToKanban();
            closeFilterPanel();
        });

        // Apply created date filter
        $('#applyCreatedDateFilter').on('click', function() {
            const selectedRange = $('input[name="dateRange"]:checked').val();
            
            if (selectedRange) {
                activeFilters.created_on.range = selectedRange;
                
                if (selectedRange === 'custom') {
                    const startDate = $('#createdStartDate').val();
                    const endDate = $('#createdEndDate').val();
                    
                    if (!startDate || !endDate) {
                        show_toastr('error', 'Please select both start and end dates for custom range.', 'error');
                        return;
                    }
                    
                    activeFilters.created_on.start_date = startDate;
                    activeFilters.created_on.end_date = endDate;
                } else {
                    activeFilters.created_on.start_date = '';
                    activeFilters.created_on.end_date = '';
                }
                
                updateFilterIndicator();
                applyFiltersToKanban();
                closeFilterPanel();
            } else {
                // If no range selected, clear the filter
                activeFilters.created_on = { range: '', start_date: '', end_date: '' };
                updateFilterIndicator();
                applyFiltersToKanban();
                closeFilterPanel();
            }
        });

        // Apply updated date filter
        $('#applyUpdatedDateFilter').on('click', function() {
            const selectedRange = $('input[name="updatedDateRange"]:checked').val();
            
            if (selectedRange) {
                activeFilters.updated_on.range = selectedRange;
                
                if (selectedRange === 'custom') {
                    const startDate = $('#updatedStartDate').val();
                    const endDate = $('#updatedEndDate').val();
                    
                    if (!startDate || !endDate) {
                        show_toastr('error', 'Please select both start and end dates for custom range.', 'error');
                        return;
                    }
                    
                    activeFilters.updated_on.start_date = startDate;
                    activeFilters.updated_on.end_date = endDate;
                } else {
                    activeFilters.updated_on.start_date = '';
                    activeFilters.updated_on.end_date = '';
                }
                
                updateFilterIndicator();
                applyFiltersToKanban();
                closeFilterPanel();
            } else {
                // If no range selected, clear the filter
                activeFilters.updated_on = { range: '', start_date: '', end_date: '' };
                updateFilterIndicator();
                applyFiltersToKanban();
                closeFilterPanel();
            }
        });

        // Apply source filter
        $('#applySourceFilter').on('click', function() {
            // Collect selected sources
            const selectedSources = [];
            $('#sourceList input[type="checkbox"]:checked').each(function() {
                selectedSources.push($(this).val());
            });

            activeFilters.sources = selectedSources;
            updateFilterIndicator();
            applyFiltersToKanban();
            closeFilterPanel();
        });

        // Update filter indicator
        function updateFilterIndicator() {
            const hasFilters = activeFilters.assigned_to.length > 0 || 
                              activeFilters.tags.length > 0 || 
                              activeFilters.created_on.range !== '' ||
                              activeFilters.updated_on.range !== '' ||
                              activeFilters.sources.length > 0;
            $('#openFilterPanel').toggleClass('has-filters', hasFilters);
            
            if (hasFilters) {
                if ($('#openFilterPanel .filter-applied-indicator').length === 0) {
                    $('#openFilterPanel').css('position', 'relative').append('<span class="filter-applied-indicator"></span>');
                }
            } else {
                $('#openFilterPanel .filter-applied-indicator').remove();
            }
        }

        // Apply filters to kanban
        function applyFiltersToKanban() {
            if (activeFilters.assigned_to.length === 0 && 
                activeFilters.tags.length === 0 && 
                activeFilters.created_on.range === '' &&
                activeFilters.updated_on.range === '' &&
                activeFilters.sources.length === 0) {
                // Show all leads if no filters
                $('.kanban-card').show();
                updateStageCountsAfterFilter();
                return;
            }

            $.ajax({
                url: '<?php echo e(route('leads.filter')); ?>',
                type: 'GET',
                data: {
                    assigned_to: activeFilters.assigned_to,
                    tags: activeFilters.tags,
                    created_on_range: activeFilters.created_on.range,
                    created_on_start: activeFilters.created_on.start_date,
                    created_on_end: activeFilters.created_on.end_date,
                    updated_on_range: activeFilters.updated_on.range,
                    updated_on_start: activeFilters.updated_on.start_date,
                    updated_on_end: activeFilters.updated_on.end_date,
                    sources: activeFilters.sources
                },
                success: function(response) {
                    // Hide all leads first
                    $('.kanban-card').hide();
                    
                    // Show only filtered leads
                    response.leads.forEach(lead => {
                        $(`.kanban-card[data-id="${lead.id}"]`).show();
                    });
                    
                    updateStageCountsAfterFilter();
                },
                error: function(xhr) {
                    console.error('Filter failed:', xhr);
                    show_toastr('error', 'Filter failed. Please try again.', 'error');
                }
            });
        }

        // Update stage counts after filtering
        function updateStageCountsAfterFilter() {
            $('.kanban-col').each(function() {
                const stageColumn = $(this);
                const visibleLeads = stageColumn.find('.kanban-card:visible').length;
                stageColumn.find('.count').text(visibleLeads);
            });
        }

        // Load tags for filter
        function loadTags() {
            $.ajax({
                url: '<?php echo e(route('leads.filter.tags')); ?>',
                type: 'GET',
                success: function(response) {
                    allTags = response.tags;
                    renderTagList();
                },
                error: function(xhr) {
                    console.error('Failed to load tags:', xhr);
                }
            });
        }

        // Render tag list
        function renderTagList(searchTerm = '') {
            const tagList = $('#tagList');
            tagList.empty();

            // Filter and add tags
            const filteredTags = allTags.filter(tag => 
                tag.name.toLowerCase().includes(searchTerm.toLowerCase())
            );

            filteredTags.forEach(tag => {
                const tagItem = $(`
                    <div class="user-item">
                        <input type="checkbox" value="${tag.id}" id="tag_${tag.id}" ${activeFilters.tags.includes(tag.id.toString()) ? 'checked' : ''}>
                        <label for="tag_${tag.id}">${tag.name}</label>
                    </div>
                `);
                tagList.append(tagItem);
            });
        }

        // Load sources for filter
        function loadSources() {
            $.ajax({
                url: '<?php echo e(route('leads.filter.sources')); ?>',
                type: 'GET',
                success: function(response) {
                    allSources = response.sources;
                    renderSourceList();
                },
                error: function(xhr) {
                    console.error('Failed to load sources:', xhr);
                }
            });
        }

        // Render source list
        function renderSourceList(searchTerm = '') {
            const sourceList = $('#sourceList');
            sourceList.empty();

            // Filter and add sources
            const filteredSources = allSources.filter(source => 
                source.name.toLowerCase().includes(searchTerm.toLowerCase())
            );

            filteredSources.forEach(source => {
                const sourceItem = $(`
                    <div class="user-item">
                        <input type="checkbox" value="${source.id}" id="source_${source.id}" ${activeFilters.sources.includes(source.id.toString()) ? 'checked' : ''}>
                        <label for="source_${source.id}">${source.name}</label>
                    </div>
                `);
                sourceList.append(sourceItem);
            });
        }

        // Handle user item clicks
        $(document).on('click', '.user-item', function(e) {
            if (e.target.type !== 'checkbox') {
                const checkbox = $(this).find('input[type="checkbox"]');
                checkbox.prop('checked', !checkbox.prop('checked'));
            }
        });
    });
</script>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\new_laravel_project\omx-new-saas\resources\views/leads/index.blade.php ENDPATH**/ ?>